<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-button { 
            padding: 10px 20px; 
            margin: 10px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .test-button:hover { background: #0056b3; }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>导出功能修复测试</h1>
    
    <button class="test-button" onclick="testExportManager()">测试 ExportManager</button>
    <button class="test-button" onclick="testExcelExport()">测试 Excel 导出</button>
    <button class="test-button" onclick="testCSVExport()">测试 CSV 导出</button>
    
    <div id="status"></div>
    
    <!-- 引入jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 引入组件库 -->
    <script src="/static/dist/js/components.min.js"></script>
    
    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function testExportManager() {
            try {
                if (typeof ExportManager !== 'undefined') {
                    showStatus('✅ ExportManager 已正确加载！', 'success');
                    console.log('ExportManager:', ExportManager);
                    
                    // 测试初始化
                    ExportManager.init({
                        fileNamePrefix: '测试数据',
                        showSuccessMessage: true
                    });
                    
                    showStatus('✅ ExportManager 初始化成功！', 'success');
                } else {
                    showStatus('❌ ExportManager 未定义', 'error');
                }
            } catch (error) {
                showStatus(`❌ 测试失败: ${error.message}`, 'error');
                console.error('测试错误:', error);
            }
        }
        
        function testExcelExport() {
            try {
                if (typeof ExportManager === 'undefined') {
                    showStatus('❌ ExportManager 未加载', 'error');
                    return;
                }
                
                // 创建测试数据
                const testData = [
                    ['姓名', '年龄', '城市'],
                    ['张三', '25', '北京'],
                    ['李四', '30', '上海'],
                    ['王五', '28', '广州']
                ];
                
                // 测试 Excel 导出函数是否存在
                if (typeof ExportManager.exportToExcel === 'function') {
                    ExportManager.exportToExcel(testData, '测试数据');
                    showStatus('✅ Excel 导出功能正常！', 'success');
                } else {
                    showStatus('❌ Excel 导出函数不存在', 'error');
                }
            } catch (error) {
                showStatus(`❌ Excel 导出测试失败: ${error.message}`, 'error');
                console.error('Excel导出错误:', error);
            }
        }
        
        function testCSVExport() {
            try {
                if (typeof ExportManager === 'undefined') {
                    showStatus('❌ ExportManager 未加载', 'error');
                    return;
                }
                
                // 创建测试数据
                const testData = [
                    ['姓名', '年龄', '城市'],
                    ['张三', '25', '北京'],
                    ['李四', '30', '上海'],
                    ['王五', '28', '广州']
                ];
                
                // 测试 CSV 导出函数是否存在
                if (typeof ExportManager.exportToCSV === 'function') {
                    ExportManager.exportToCSV(testData, '测试数据');
                    showStatus('✅ CSV 导出功能正常！', 'success');
                } else {
                    showStatus('❌ CSV 导出函数不存在', 'error');
                }
            } catch (error) {
                showStatus(`❌ CSV 导出测试失败: ${error.message}`, 'error');
                console.error('CSV导出错误:', error);
            }
        }
        
        // 页面加载完成后自动检测
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('页面加载完成，检查 ExportManager...');
                console.log('window.ExportManager:', window.ExportManager);
                console.log('typeof ExportManager:', typeof ExportManager);
                
                if (typeof ExportManager !== 'undefined') {
                    showStatus('🎉 页面加载后 ExportManager 可用！', 'success');
                } else {
                    showStatus('⚠️ 页面加载后 ExportManager 不可用', 'error');
                }
            }, 500);
        });
    </script>
</body>
</html>
# HDSC查询应用 - 本地部署方案

由于网络连接问题无法拉取Docker基础镜像，我为您提供以下替代部署方案：

## 方案一：本地Python环境部署

### 1. 环境要求
- Python 3.11+
- Node.js 18+
- Git

### 2. 部署步骤

#### 步骤1：安装Python依赖
```bash
# 激活虚拟环境（如果有）
# .venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装Python依赖
pip install -r requirements.txt
```

#### 步骤2：安装前端依赖并构建
```bash
# 安装Node.js依赖
npm install

# 构建前端资源
npm run build
```

#### 步骤3：启动应用
```bash
# 开发模式
python run.py

# 或生产模式
gunicorn -c gunicorn_config.py run:app
```

### 3. 访问应用
- 开发模式：http://localhost:5000
- 生产模式：http://localhost:5000

## 方案二：Docker离线部署（推荐）

### 1. 准备离线镜像
如果您有网络良好的环境，可以在那里构建镜像并导出：

```bash
# 在网络良好的环境中构建
docker build -t hdsc-query-app:v1.0 .

# 导出镜像
docker save -o hdsc-query-app-v1.0.tar hdsc-query-app:v1.0
```

### 2. 导入镜像
将tar文件传输到目标服务器后：

```bash
# 导入镜像
docker load -i hdsc-query-app-v1.0.tar

# 运行容器
docker run -d --name hdsc-app -p 5000:5000 hdsc-query-app:v1.0
```

## 方案三：使用Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  hdsc-app:
    image: hdsc-query-app:v1.0
    container_name: hdsc-query-app
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
    volumes:
      - ./logs:/app/logs
      - ./cache:/app/cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

启动：
```bash
docker-compose up -d
```

## 故障排除

### 网络连接问题
如果遇到网络连接问题，可以：
1. 配置Docker镜像加速器
2. 使用企业内网镜像仓库
3. 使用离线部署方案

### 依赖兼容性问题
当前配置已解决numpy/pandas兼容性问题：
- numpy==1.25.2
- pandas==2.0.3

### 性能优化
- 使用gunicorn多进程部署
- 配置反向代理（nginx）
- 启用缓存机制

## 技术支持

如需进一步协助，请提供：
1. 错误日志
2. 系统环境信息
3. 网络配置详情

---
生成时间：$(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
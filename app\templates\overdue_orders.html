{% extends "base.html" %}

{% block title %}逾期订单查询_{{ version }}{% endblock %}

{% block styles %}
{{ super() }}
<!-- 引入Dashboard风格的舒适响应式样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-unified-table.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-style-responsive.css') }}">
<!-- 引入企业级逾期订单分页样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/overdue-orders-pagination.css') }}">
{% endblock %}


{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">逾期订单查询</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <!-- 刷新按钮 -->
                    <a href="{{ url_for('main.query.overdue_orders', refresh=1) }}" class="btn btn-sm btn-outline-primary" id="refreshData">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="ExportManager.exportData('excel', {dataSource: 'overdueOrders'})">
                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="ExportManager.exportData('csv', {dataSource: 'overdueOrders'})">
                        <i class="bi bi-file-earmark-text"></i> 导出CSV
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 缓存信息提示 -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i>
            数据最后更新时间: <strong>{{ last_update }}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        </div>

        <!-- 加载状态更新区 -->
        <div id="loadingStatus" class="mb-2"></div>
        
        <!-- 表格控制区：搜索框与分页大小选择器 -->
        <div class="row mb-3 align-items-center table-controls-row">
            <div class="col-md-6">
                <form id="searchForm" action="{{ url_for('main.query.overdue_orders') }}" method="GET">
                    <!-- 保留当前的limit参数 -->
                    <input type="hidden" name="limit" value="{{ page_limit }}">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" id="tableSearch" name="search" class="form-control" placeholder="全局搜索..." aria-label="搜索" value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit">搜索</button>
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end">
                    <label for="pageSizeSelect" class="me-2 text-nowrap">每页显示:</label>
                    <select id="pageSizeSelect" class="form-select form-select-sm" style="width: auto;">
                        <option value="10" {% if page_limit == 10 %}selected{% endif %}>10 条</option>
                        <option value="25" {% if page_limit == 25 %}selected{% endif %}>25 条</option>
                        <option value="50" {% if page_limit == 50 %}selected{% endif %}>50 条</option>
                        <option value="100" {% if page_limit == 100 %}selected{% endif %}>100 条</option>
                        <option value="200" {% if page_limit == 200 %}selected{% endif %}>200 条</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 逾期订单内容 -->
        <div class="overdue-content">
            {% if overdue_results and overdue_results.results and overdue_results.results|length > 0 %}
                <div class="pt-3">
                    <div class="alert alert-success auto-dismiss-alert">
                        {% if search_query %}
                            搜索"{{ search_query }}"：共找到 {{ total_records }} 条匹配记录
                        {% else %}
                            共找到 {{ overdue_results.results|length }} 条逾期记录
                        {% endif %}
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover data-table" style="width:100%" data-type="overdue" id="overdueTable">
                        <thead>
                            <tr>
                                <th></th> <!-- 用于响应式详情控制 -->
                                {% for column in overdue_results.columns %}
                                <th>{{ column }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {%- for row in overdue_results.results -%}
                            <tr{% if '备注' in row %} data-status="{{ row['备注'] }}"{% endif %}>
                                <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                {% for column in overdue_results.columns %}
                                <td {% if column == '备注' %}class="{{ row.get(column, '') }}"{% endif %}
                                   {% if column in ['订单编号', '客户手机', '客户姓名'] %}style="white-space: nowrap; min-width: 120px;"{% endif %}
                                   {% if column in ['当前待收', '总待收', '成本'] %}class="amount-field"{% endif %}
                                   {% if column in ['业务', '客服', '产品', '产品类型'] %}class="category-field"{% endif %}
                                   {% if column in ['订单日期', '账单日期', '首次逾期日期'] %}class="date-field"{% endif %}
                                   {% if column in ['逾期天数', '逾期期数', '首次逾期期数'] %}class="status-field"{% endif %}>
                                    {% if column in row %}
                                        {% if column in ['当前待收', '总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                            {{ "%.2f"|format(row[column]|float) }}
                                        {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                            <span class="badge bg-danger">{{ row[column] }}</span>
                                        {% elif column in ['业务', '客服'] %}
                                            <span class="badge bg-primary">{{ row[column] }}</span>
                                        {% elif column in ['产品', '产品类型'] %}
                                            {% if '电商' in row[column] %}
                                                <span class="badge bg-info text-dark" data-product-type="电商">{{ row[column] }}</span>
                                            {% elif '租赁' in row[column] %}
                                                <span class="badge bg-info text-dark" data-product-type="租赁">{{ row[column] }}</span>
                                            {% else %}
                                                <span class="badge bg-info text-dark">{{ row[column] }}</span>
                                            {% endif %}
                                        {% elif column in ['订单日期', '账单日期', '首次逾期日期'] %}
                                            <span class="badge bg-secondary">{{ row[column] }}</span>
                                        {% else %}
                                            {{ row[column] }}
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 优化后的分页控件 -->
                <div class="pagination-container mt-3">
                    <nav aria-label="逾期订单分页">
                        <ul class="pagination justify-content-center">
                            <!-- 上一页按钮 -->
                            <li class="page-item {% if current_page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=current_page-1, limit=page_limit, search=search_query) if current_page > 1 else '#' }}">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </a>
                            </li>
                            
                            <!-- 页码导航，使用数据验证的总页数 -->
                            {% if total_pages > 1 %}
                                <!-- 智能分页控件，显示当前页附近的页码 -->
                                {% set start_page = [1, current_page - 2]|max %}
                                {% set end_page = [total_pages, current_page + 2]|min %}
                                
                                <!-- 确保至少显示5个页码 -->
                                {% if end_page - start_page < 4 %}
                                    {% if current_page < total_pages - 2 %}
                                        {% set start_page = [1, end_page - 4]|max %}
                                    {% else %}
                                        {% set start_page = [1, total_pages - 4]|max %}
                                    {% endif %}
                                {% endif %}
                                
                                <!-- 显示第一页和省略号 -->
                                {% if start_page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=1, limit=page_limit, search=search_query) }}">1</a>
                                    </li>
                                    {% if start_page > 2 %}
                                        <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                    {% endif %}
                                {% endif %}
                                
                                <!-- 页码列表 -->
                                {% for p in range(start_page, end_page + 1) %}
                                    <li class="page-item {% if p == current_page %}active{% endif %}">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=p, limit=page_limit, search=search_query) }}">{{ p }}</a>
                                    </li>
                                {% endfor %}
                                
                                <!-- 显示最后一页和省略号 -->
                                {% if end_page < total_pages %}
                                    {% if end_page < total_pages - 1 %}
                                        <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                    {% endif %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=total_pages, limit=page_limit, search=search_query) }}">{{ total_pages }}</a>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            {% endif %}
                            
                            <!-- 下一页按钮 -->
                            <li class="page-item {% if current_page >= total_pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=current_page+1, limit=page_limit, search=search_query) if current_page < total_pages else '#' }}">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    
                    <!-- 移除旧的页面大小选择器，改在顶部显示 -->
                    
                    <!-- 数据信息提示 -->
                    <div class="mt-2 text-center text-muted small">
                        {% if total_records > 0 %}
                            当前显示 {{ (current_page-1) * page_limit + 1 }} - {{ (current_page-1) * page_limit + overdue_results.results|length }} 条，共 {{ total_records }} 条记录
                        {% else %}
                            没有记录
                        {% endif %}
                    </div>
                </div>
                
                <!-- 注释掉DataTables自带的分页控件，由JavaScript动态处理 -->
                <script>
                    // 在DOM加载完成后执行
                    document.addEventListener('DOMContentLoaded', function() {
                        // 隐藏DataTables自带的分页控件
                        setTimeout(function() {
                            var dtPagination = document.querySelector('.dataTables_paginate');
                            if (dtPagination) {
                                dtPagination.style.display = 'none';
                            }
                            
                            var dtInfo = document.querySelector('.dataTables_info');
                            if (dtInfo) {
                                dtInfo.style.display = 'none';
                            }
                        }, 100);
                    });
                </script>
                
            {% elif overdue_results and 'error' in overdue_results %}
                <div class="alert alert-danger mt-3">
                    获取逾期数据失败: {{ overdue_results.error }}
                </div>
            {% else %}
                <div class="alert alert-info mt-3">
                    点击侧边栏的"逾期订单查询"按钮获取逾期订单数据。
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入SheetJS库用于Excel导出（本地文件） -->
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>

<!-- 将后端数据存储到全局变量中，供前端JavaScript使用 -->
<script type="text/javascript">
// 初始化全局变量
window.overdueResults = {};

// 初始化数据
{% if overdue_results %}
// 从后端接收数据 - 预处理为JavaScript字符串
window.overdueResults = {{ overdue_results|tojson|safe }};
// 记录接收到的数据信息
if (window.overdueResults && window.overdueResults.results) {
    console.log('接收到逾期数据: ' + window.overdueResults.results.length + ' 条记录');
}
{% else %}
// 初始化空的数据对象
window.overdueResults = {};
{% endif %}

// 在页面加载完成后，为导出管理器添加自定义逾期订单处理逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 确保ExportManager存在
    if (window.ExportManager) {
        // 为ExportManager添加逾期订单数据源处理器
        ExportManager.addDataSourceHandler('overdueOrders', function(format, options = {}) {
            // 获取当前搜索查询参数（如果有）
            const urlParams = new URLSearchParams(window.location.search);
            const searchQuery = urlParams.get('search') || '';
            
            // 直接检查window.overdueResults
            if (!window.overdueResults || !window.overdueResults.results || window.overdueResults.results.length === 0) {
                alert('没有可导出的数据');
                return Promise.reject(new Error('没有可导出的数据'));
            }
            
            // 获取分页信息
            const pagination = window.overdueResults.pagination || {};
            const totalRecords = pagination.total || 0;
            
            // 构建文件名
            const fileName = `逾期订单_${searchQuery ? '筛选数据_' : '全部数据_'}${new Date().toISOString().slice(0, 10)}`;
            
            // 使用统一的确认和加载状态显示
            return ExportManager.confirmExport(totalRecords)
                .then(confirmed => {
                    if (!confirmed) return Promise.reject(new Error('用户取消导出'));
                    
                    // 显示加载状态
                    if (window.LoadingController) {
                        LoadingController.showLoading('正在准备导出数据...');
                    }
                    
                    // 使用服务器端导出功能获取全部数据，并传递搜索查询
                    return fetch(`/api/export_all_overdue?format=${format}&search=${encodeURIComponent(searchQuery)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('服务器响应错误: ' + response.status);
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            return {
                                fileName: fileName,
                                data: blob,
                                totalRecords: totalRecords,
                                searchQuery: searchQuery
                            };
                        });
                });
        });
    }
});

// 如果URL中包含分页参数，记录下来
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');
    const limit = urlParams.get('limit');
    
    if (page) {
        console.log('当前页码: ' + page);
    }
    
    if (limit) {
        console.log('每页记录数: ' + limit);
    }
    
    // 初始化数据表格
    const dataTable = initializeOverdueDataTable();
});

// 初始化逾期订单数据表格 - 使用Dashboard风格
function initializeOverdueDataTable() {
    const tableElement = document.getElementById('overdueTable');
    if (!tableElement) return;
    
    // 检查表格是否已初始化
    if ($.fn.DataTable.isDataTable(tableElement)) {
        $(tableElement).DataTable().destroy();
    }
    
    // 使用简洁的DataTables配置，参考Dashboard页面
    const dataTable = $(tableElement).DataTable({
        responsive: {
            details: {
                type: 'column',
                target: 0 // 使用第一列作为控制列
            }
        },
        autoWidth: false,
        scrollX: true,
        scrollCollapse: true,
        columnDefs: [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0,
                width: "35px"
            },
            {
                targets: '_all',
                className: 'dt-head-center dt-body-center'
            }
        ],
        order: [[1, 'desc']],
        dom: "<'row'<'col-sm-12'>>rt<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        searching: false,
        paging: false,
        language: {
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项结果，共 0 项",
            zeroRecords: "没有匹配结果",
            emptyTable: "暂无数据"
        }
    });
    
    // 响应式调整
    setTimeout(() => {
        dataTable.columns.adjust().responsive.recalc();
        console.log('逾期订单表格初始化完成 - Dashboard风格');
    }, 100);
    
    return dataTable;
}

// 自定义表格控件布局 - 简化版本
function customizeTableControls() {
    // 由企业级管理器统一处理，无需额外操作
    console.log('表格控件布局已由企业级管理器统一处理');
}

// 表格初始化完成后执行样式调整
$(document).ready(function() {
    // 页面加载完成后执行列宽调整
    $(window).on('load', function() {
        setTimeout(function() {
            if ($.fn.DataTable.isDataTable('#overdueTable')) {
                const table = $('#overdueTable').DataTable();
                table.columns.adjust().draw(false);
            }
        }, 300);
    });
    
    // DOM完全加载后执行
    $(document).on('init.dt', function(e, settings) {
        // 获取DataTable实例所在的容器
        const wrapper = $(settings.nTableWrapper);
        
        // 添加Bootstrap样式到搜索框
        wrapper.find('.dataTables_filter input').addClass('form-control form-control-sm ms-1')
            .css('width', '150px')
            .attr('placeholder', '搜索...');
        
        // 确保搜索框样式正确
        wrapper.find('.dataTables_filter').css({
            'display': 'inline-block',
            'vertical-align': 'middle'
        });
        
        // 调整控件容器
        wrapper.find('.dataTables_filter').parent().css({
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'flex-end',
            'flex-wrap': 'nowrap',
            'width': '100%'
        });
    });
});
</script>

<!-- 添加下拉选择器事件处理 -->
<script>
// 处理分页大小选择器变更事件
document.addEventListener('DOMContentLoaded', function() {
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            // 构建新的URL，保留当前搜索参数，但重置为第1页
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('page', '1'); // 切换每页条数时重置到第1页
            currentUrl.searchParams.set('limit', this.value);
            // 保留搜索关键词参数，如果存在的话
            const searchQuery = document.getElementById('tableSearch').value;
            if (searchQuery) {
                currentUrl.searchParams.set('search', searchQuery);
            }
            window.location.href = currentUrl.toString();
        });
    }
    
    // 表格搜索功能现在已经通过表单提交方式实现，不再需要客户端处理

    // 如果URL中有搜索参数且搜索框存在，设置搜索框的值
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam) {
        const searchInput = document.getElementById('tableSearch');
        if (searchInput) {
            searchInput.value = searchParam;
        }
    }
});
</script>

<!-- 引入简化的控制器，与 Dashboard 保持一致 -->
<script src="{{ url_for('static', filename='js/controllers/loading-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/data-query-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/app.js') }}"></script>
<!-- 引入企业级逾期订单分页管理器 -->
<script src="{{ url_for('static', filename='js/enterprise/overdue-orders-pagination-manager.js') }}"></script>
{% endblock %}

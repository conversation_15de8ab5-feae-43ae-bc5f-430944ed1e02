{% extends "base.html" %}

{% block title %}逾期订单查询_{{ version }}{% endblock %}

{% block styles %}
{{ super() }}
<!-- 现代化逾期订单页面样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern-overdue-orders.css') }}">
<!-- 响应式数据表格样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-data-table.css') }}">
<!-- 移动端优化样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-optimized.css') }}">
{% endblock %}


{% block content %}
<div class="modern-overdue-container">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        逾期订单查询
                    </h1>
                    <p class="page-subtitle">实时监控和管理逾期订单数据</p>
                </div>
                <div class="header-actions">
                    <button class="action-btn refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新数据</span>
                    </button>
                    <div class="export-dropdown">
                        <button class="action-btn export-btn" onclick="toggleExportMenu()">
                            <i class="fas fa-download"></i>
                            <span>导出数据</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="export-menu" id="exportMenu">
                            <button onclick="exportData('excel')" class="export-option">
                                <i class="fas fa-file-excel"></i>
                                Excel格式
                            </button>
                            <button onclick="exportData('csv')" class="export-option">
                                <i class="fas fa-file-csv"></i>
                                CSV格式
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据状态卡片 -->
        <div class="status-cards">
            <div class="status-card info-card">
                <div class="card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">最后更新</div>
                    <div class="card-value">{{ last_update or '暂无数据' }}</div>
                </div>
            </div>
            <div class="status-card count-card">
                <div class="card-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">总记录数</div>
                    <div class="card-value" id="totalRecords">{{ total_records or 0 }}</div>
                </div>
            </div>
            <div class="status-card filter-card">
                <div class="card-icon">
                    <i class="fas fa-filter"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">当前显示</div>
                    <div class="card-value" id="currentDisplay">
                        {% if search_query %}筛选结果{% else %}全部数据{% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载数据...</div>
        </div>

        <!-- 搜索和筛选控制区 -->
        <div class="search-controls">
            <div class="search-section">
                <div class="search-box">
                    <form id="searchForm" action="{{ url_for('main.query.overdue_orders') }}" method="GET">
                        <input type="hidden" name="limit" value="{{ page_limit }}">
                        <div class="search-input-group">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   id="tableSearch"
                                   name="search"
                                   class="search-input"
                                   placeholder="搜索订单编号、客户姓名、手机号..."
                                   value="{{ search_query }}"
                                   autocomplete="off">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search_query %}
                            <button type="button" class="clear-search-btn" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
                <div class="filter-controls">
                    <div class="page-size-selector">
                        <label for="pageSizeSelect">每页显示</label>
                        <select id="pageSizeSelect" class="page-size-select">
                            <option value="10" {% if page_limit == 10 %}selected{% endif %}>10 条</option>
                            <option value="25" {% if page_limit == 25 %}selected{% endif %}>25 条</option>
                            <option value="50" {% if page_limit == 50 %}selected{% endif %}>50 条</option>
                            <option value="100" {% if page_limit == 100 %}selected{% endif %}>100 条</option>
                            <option value="200" {% if page_limit == 200 %}selected{% endif %}>200 条</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div class="data-container">
            {% if overdue_results and overdue_results.results and overdue_results.results|length > 0 %}
                <!-- 结果统计 -->
                <div class="result-summary">
                    <div class="summary-info">
                        {% if search_query %}
                            <i class="fas fa-search"></i>
                            搜索"<strong>{{ search_query }}</strong>"：找到 <strong>{{ total_records }}</strong> 条匹配记录
                        {% else %}
                            <i class="fas fa-list"></i>
                            共找到 <strong>{{ overdue_results.results|length }}</strong> 条逾期记录
                        {% endif %}
                    </div>
                </div>

                <!-- 桌面端表格视图 -->
                <div class="desktop-table-view">
                    <div class="table-container">
                        <table class="modern-data-table" id="overdueTable">
                            <thead>
                                <tr>
                                    {% for column in overdue_results.columns %}
                                    <th class="table-header" data-column="{{ column }}">
                                        <span class="header-text">{{ column }}</span>
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                {%- for row in overdue_results.results -%}
                                <tr class="table-row" data-row-index="{{ loop.index0 }}">
                                    {% for column in overdue_results.columns %}
                                    <td class="table-cell {{ column|replace(' ', '-')|lower }}-cell"
                                        data-label="{{ column }}">
                                        {% if column in row %}
                                            {% if column in ['当前待收', '总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                                <span class="amount-value">{{ "%.2f"|format(row[column]|float) }}</span>
                                            {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                                <span class="status-badge danger">{{ row[column] }}</span>
                                            {% elif column in ['业务', '客服'] %}
                                                <span class="status-badge primary">{{ row[column] }}</span>
                                            {% elif column in ['产品', '产品类型'] %}
                                                {% if '电商' in row[column] %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% elif '租赁' in row[column] %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% else %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% endif %}
                                            {% elif column in ['订单日期', '账单日期', '首次逾期日期'] %}
                                                <span class="date-value">{{ row[column] }}</span>
                                            {% else %}
                                                <span class="text-value">{{ row[column] }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="empty-value">-</span>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 移动端卡片视图 -->
                <div class="mobile-card-view">
                    <div class="cards-container" id="cardsContainer">
                        {%- for row in overdue_results.results -%}
                        <div class="data-card" data-row-index="{{ loop.index0 }}">
                            <div class="card-header">
                                <div class="card-title">
                                    {% if '订单编号' in row %}
                                        <span class="order-number">{{ row['订单编号'] }}</span>
                                    {% endif %}
                                    {% if '逾期天数' in row and row['逾期天数']|string|int(0) > 0 %}
                                        <span class="overdue-badge">逾期{{ row['逾期天数'] }}天</span>
                                    {% endif %}
                                </div>
                                <div class="card-actions">
                                    <button class="card-expand-btn" onclick="toggleCardDetails({{ loop.index0 }})">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-summary">
                                {% if '客户姓名' in row %}
                                <div class="summary-item">
                                    <span class="item-label">客户</span>
                                    <span class="item-value">{{ row['客户姓名'] }}</span>
                                </div>
                                {% endif %}
                                {% if '当前待收' in row and row['当前待收']|string|float(0) > 0 %}
                                <div class="summary-item">
                                    <span class="item-label">待收金额</span>
                                    <span class="item-value amount">{{ "%.2f"|format(row['当前待收']|float) }}</span>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-details" id="cardDetails{{ loop.index0 }}">
                                {% for column in overdue_results.columns %}
                                    {% if column not in ['订单编号', '客户姓名', '当前待收', '逾期天数'] and column in row %}
                                    <div class="detail-item">
                                        <span class="detail-label">{{ column }}</span>
                                        <span class="detail-value">
                                            {% if column in ['总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                                {{ "%.2f"|format(row[column]|float) }}
                                            {% else %}
                                                {{ row[column] }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 现代化分页控件 -->
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        {% if total_records > 0 %}
                            显示第 <strong>{{ (current_page-1) * page_limit + 1 }}</strong> -
                            <strong>{{ (current_page-1) * page_limit + overdue_results.results|length }}</strong> 条，
                            共 <strong>{{ total_records }}</strong> 条记录
                        {% else %}
                            没有记录
                        {% endif %}
                    </div>

                    {% if total_pages > 1 %}
                    <nav class="pagination-nav" aria-label="逾期订单分页">
                        <div class="pagination-controls">
                            <!-- 上一页按钮 -->
                            <a class="pagination-btn prev-btn {% if current_page == 1 %}disabled{% endif %}"
                               href="{{ url_for('main.query.overdue_orders', page=current_page-1, limit=page_limit, search=search_query) if current_page > 1 else '#' }}">
                                <i class="fas fa-chevron-left"></i>
                                <span class="btn-text">上一页</span>
                            </a>

                            <!-- 页码按钮 -->
                            <div class="page-numbers">
                                {% set start_page = [1, current_page - 2]|max %}
                                {% set end_page = [total_pages, current_page + 2]|min %}

                                {% if start_page > 1 %}
                                    <a class="page-btn" href="{{ url_for('main.query.overdue_orders', page=1, limit=page_limit, search=search_query) }}">1</a>
                                    {% if start_page > 2 %}
                                        <span class="page-ellipsis">...</span>
                                    {% endif %}
                                {% endif %}

                                {% for p in range(start_page, end_page + 1) %}
                                    <a class="page-btn {% if p == current_page %}active{% endif %}"
                                       href="{{ url_for('main.query.overdue_orders', page=p, limit=page_limit, search=search_query) }}">{{ p }}</a>
                                {% endfor %}

                                {% if end_page < total_pages %}
                                    {% if end_page < total_pages - 1 %}
                                        <span class="page-ellipsis">...</span>
                                    {% endif %}
                                    <a class="page-btn" href="{{ url_for('main.query.overdue_orders', page=total_pages, limit=page_limit, search=search_query) }}">{{ total_pages }}</a>
                                {% endif %}
                            </div>

                            <!-- 下一页按钮 -->
                            <a class="pagination-btn next-btn {% if current_page >= total_pages %}disabled{% endif %}"
                               href="{{ url_for('main.query.overdue_orders', page=current_page+1, limit=page_limit, search=search_query) if current_page < total_pages else '#' }}">
                                <span class="btn-text">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </nav>
                    {% endif %}
                </div>

            {% elif overdue_results and 'error' in overdue_results %}
                <!-- 错误状态 -->
                <div class="error-state">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-content">
                        <h3 class="error-title">数据加载失败</h3>
                        <p class="error-message">{{ overdue_results.error }}</p>
                        <button class="retry-btn" onclick="retryLoadData()">
                            <i class="fas fa-redo"></i>
                            重试加载
                        </button>
                    </div>
                </div>
            {% else %}
                <!-- 空状态 -->
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="empty-content">
                        <h3 class="empty-title">暂无数据</h3>
                        <p class="empty-message">
                            {% if search_query %}
                                没有找到匹配"{{ search_query }}"的逾期订单
                            {% else %}
                                当前没有逾期订单数据
                            {% endif %}
                        </p>
                        {% if search_query %}
                        <button class="clear-search-btn" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                            清除搜索
                        </button>
                        {% else %}
                        <button class="load-data-btn" onclick="loadInitialData()">
                            <i class="fas fa-download"></i>
                            加载数据
                        </button>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 现代化逾期订单页面脚本 -->
<script type="text/javascript">
// 全局配置和数据
const OverdueOrdersApp = {
    // 应用配置
    config: {
        pageSize: {{ page_limit or 10 }},
        currentPage: {{ current_page or 1 }},
        totalPages: {{ total_pages or 1 }},
        totalRecords: {{ total_records or 0 }},
        searchQuery: '{{ search_query or "" }}',
        apiEndpoints: {
            refresh: '{{ url_for("main.query.overdue_orders", refresh=1) }}',
            export: '/api/export_all_overdue'
        }
    },

    // 数据存储
    data: {
        results: {{ overdue_results.results|tojson|safe if overdue_results and overdue_results.results else '[]' }},
        columns: {{ overdue_results.columns|tojson|safe if overdue_results and overdue_results.columns else '[]' }},
        lastUpdate: '{{ last_update or "" }}'
    },

    // 应用状态
    state: {
        isLoading: false,
        isMobile: window.innerWidth <= 768,
        currentView: 'table', // 'table' or 'cards'
        sortColumn: null,
        sortDirection: 'asc'
    }
};

// 核心功能函数
const AppFunctions = {
    // 初始化应用
    init() {
        console.log('🚀 初始化现代化逾期订单应用');
        this.setupEventListeners();
        this.detectViewMode();
        this.initializeComponents();
        this.updateUI();
    },

    // 设置事件监听器
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));

        // 页面大小选择器
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', this.handlePageSizeChange.bind(this));
        }

        // 搜索表单
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }

        // 表格排序
        document.querySelectorAll('.table-header').forEach(header => {
            header.addEventListener('click', this.handleSort.bind(this));
        });
    },

    // 检测视图模式
    detectViewMode() {
        const isMobile = window.innerWidth <= 768;
        OverdueOrdersApp.state.isMobile = isMobile;
        OverdueOrdersApp.state.currentView = isMobile ? 'cards' : 'table';

        // 切换视图显示
        const desktopView = document.querySelector('.desktop-table-view');
        const mobileView = document.querySelector('.mobile-card-view');

        if (desktopView && mobileView) {
            desktopView.style.display = isMobile ? 'none' : 'block';
            mobileView.style.display = isMobile ? 'block' : 'none';
        }
    },

    // 初始化组件
    initializeComponents() {
        // 初始化文本截断功能
        setTimeout(() => {
            initializeTextTruncation();
        }, 100);
    },

    // 处理窗口大小变化
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            this.detectViewMode();
        }, 250);
    },

    // 处理页面大小变化
    handlePageSizeChange(event) {
        const newSize = event.target.value;
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', '1');
        currentUrl.searchParams.set('limit', newSize);
        window.location.href = currentUrl.toString();
    },

    // 处理搜索
    handleSearch(event) {
        this.showLoading('正在搜索...');
    },

    // 处理排序
    handleSort(event) {
        const column = event.currentTarget.dataset.column;
        if (!column) return;

        // 更新排序状态
        if (OverdueOrdersApp.state.sortColumn === column) {
            OverdueOrdersApp.state.sortDirection =
                OverdueOrdersApp.state.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            OverdueOrdersApp.state.sortColumn = column;
            OverdueOrdersApp.state.sortDirection = 'asc';
        }

        // 更新UI
        this.updateSortIcons();
        // 这里可以添加客户端排序逻辑或发送请求到服务器
    }
};

// UI更新和交互函数
const UIFunctions = {
    // 显示加载状态
    showLoading(message = '正在加载...') {
        OverdueOrdersApp.state.isLoading = true;
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'flex';
            const text = indicator.querySelector('.loading-text');
            if (text) text.textContent = message;
        }
    },

    // 隐藏加载状态
    hideLoading() {
        OverdueOrdersApp.state.isLoading = false;
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    },

    // 更新排序图标
    updateSortIcons() {
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });

        const currentHeader = document.querySelector(`[data-column="${OverdueOrdersApp.state.sortColumn}"] .sort-icon`);
        if (currentHeader) {
            const direction = OverdueOrdersApp.state.sortDirection;
            currentHeader.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} sort-icon active`;
        }
    },

    // 更新状态卡片
    updateStatusCards() {
        const totalRecordsEl = document.getElementById('totalRecords');
        if (totalRecordsEl) {
            totalRecordsEl.textContent = OverdueOrdersApp.config.totalRecords;
        }

        const currentDisplayEl = document.getElementById('currentDisplay');
        if (currentDisplayEl) {
            const searchQuery = OverdueOrdersApp.config.searchQuery;
            currentDisplayEl.textContent = searchQuery ? '筛选结果' : '全部数据';
        }
    }
};

// 全局功能函数
function refreshData() {
    UIFunctions.showLoading('正在刷新数据...');
    window.location.href = OverdueOrdersApp.config.apiEndpoints.refresh;
}

function toggleExportMenu() {
    const menu = document.getElementById('exportMenu');
    if (menu) {
        menu.classList.toggle('show');
    }
}

function exportData(format) {
    const menu = document.getElementById('exportMenu');
    if (menu) menu.classList.remove('show');

    UIFunctions.showLoading(`正在导出${format.toUpperCase()}文件...`);

    const searchQuery = OverdueOrdersApp.config.searchQuery;
    const url = `${OverdueOrdersApp.config.apiEndpoints.export}?format=${format}&search=${encodeURIComponent(searchQuery)}`;

    fetch(url)
        .then(response => {
            if (!response.ok) throw new Error('导出失败');
            return response.blob();
        })
        .then(blob => {
            // 修正文件扩展名
            let fileExtension = format;
            if (format === 'excel') {
                fileExtension = 'xlsx';
            }

            const fileName = `逾期订单_${searchQuery ? '筛选数据_' : '全部数据_'}${new Date().toISOString().slice(0, 10)}.${fileExtension}`;
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert('导出失败，请重试');
        })
        .finally(() => {
            UIFunctions.hideLoading();
        });
}

function clearSearch() {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('search');
    currentUrl.searchParams.set('page', '1');
    window.location.href = currentUrl.toString();
}

function retryLoadData() {
    refreshData();
}

function loadInitialData() {
    refreshData();
}

function toggleCardDetails(index) {
    const details = document.getElementById(`cardDetails${index}`);
    const button = details.parentElement.querySelector('.card-expand-btn i');

    if (details.style.display === 'none' || !details.style.display) {
        details.style.display = 'block';
        button.style.transform = 'rotate(180deg)';
    } else {
        details.style.display = 'none';
        button.style.transform = 'rotate(0deg)';
    }
}

// 文本截断和展开功能
function toggleTextExpansion(button) {
    const textContent = button.parentElement;
    const truncatedText = textContent.querySelector('.text-truncated');
    const fullText = textContent.querySelector('.text-full');
    const icon = button.querySelector('i');

    if (truncatedText.style.display !== 'none') {
        // 展开文本
        truncatedText.style.display = 'none';
        fullText.style.display = 'inline-block';
        icon.className = 'fas fa-chevron-up';
        button.title = '收起';
    } else {
        // 收起文本
        truncatedText.style.display = 'inline-block';
        fullText.style.display = 'none';
        icon.className = 'fas fa-chevron-down';
        button.title = '展开';
    }
}

// 初始化文本截断功能
function initializeTextTruncation() {
    // 为所有需要截断的文本添加功能
    document.querySelectorAll('.table-cell, .detail-value, .item-value').forEach(cell => {
        const text = cell.textContent.trim();

        // 只对长文本进行截断处理
        if (text.length > 30) {
            const truncatedText = text.substring(0, 30);

            cell.innerHTML = `
                <div class="text-content">
                    <span class="text-truncated" style="display: inline-block;">${truncatedText}...</span>
                    <span class="text-full" style="display: none;">${text}</span>
                    <button class="expand-btn" onclick="toggleTextExpansion(this)" title="展开">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            `;
        }
    });
}

// 应用初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 现代化逾期订单应用启动');

    // 初始化应用
    AppFunctions.init();

    // 更新UI
    UIFunctions.updateStatusCards();

    // 隐藏初始加载状态
    setTimeout(() => {
        UIFunctions.hideLoading();
    }, 500);

    // 点击外部关闭导出菜单
    document.addEventListener('click', function(event) {
        const exportDropdown = document.querySelector('.export-dropdown');
        const exportMenu = document.getElementById('exportMenu');

        if (exportDropdown && exportMenu && !exportDropdown.contains(event.target)) {
            exportMenu.classList.remove('show');
        }
    });

    console.log('✅ 应用初始化完成');
});

// 页面卸载前清理
window.addEventListener('beforeunload', function() {
    // 清理定时器
    if (AppFunctions.resizeTimer) {
        clearTimeout(AppFunctions.resizeTimer);
    }
});

// 导出全局对象供调试使用
window.OverdueOrdersApp = OverdueOrdersApp;
window.AppFunctions = AppFunctions;
window.UIFunctions = UIFunctions;
</script>

</script>

<!-- 引入必要的外部脚本 -->
<script src="{{ url_for('static', filename='js/modern-overdue-orders.js') }}"></script>
{% endblock %}

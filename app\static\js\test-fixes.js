/**
 * 测试修复效果的脚本
 */

const FixValidationTests = {
    results: [],
    
    // 运行所有验证测试
    async runAllTests() {
        console.log('=== 开始验证修复效果 ===');
        
        const tests = [
            'testNoMissingResources',
            'testDataTableColumns',
            'testChartLibraryLoading',
            'testAssetLoaderUpdates',
            'testNoTimerDuplication',
            'testPreloadWarnings'
        ];
        
        for (const testName of tests) {
            try {
                const result = await this[testName]();
                this.results.push({
                    test: testName,
                    passed: result.passed,
                    message: result.message
                });
                console.log(`${result.passed ? '✅' : '❌'} ${testName}: ${result.message}`);
            } catch (error) {
                this.results.push({
                    test: testName,
                    passed: false,
                    message: `Test failed: ${error.message}`
                });
                console.error(`❌ ${testName}: ${error.message}`);
            }
        }
        
        this.printSummary();
        return this.results;
    },
    
    // 测试是否还有缺失的资源
    testNoMissingResources() {
        const scripts = document.querySelectorAll('script[src]');
        let missingCount = 0;
        const missingResources = [];
        
        scripts.forEach(script => {
            const src = script.src;
            if (src.includes('/dist/js/') && !src.includes('chart.min.js')) {
                // 检查是否被注释掉了
                const isCommented = script.outerHTML.includes('<!--') || 
                                  script.style.display === 'none' ||
                                  script.disabled;
                
                if (!isCommented) {
                    missingCount++;
                    missingResources.push(src);
                }
            }
        });
        
        return {
            passed: missingCount === 0,
            message: missingCount === 0 ? 
                'No missing resources detected' : 
                `Found ${missingCount} potentially missing resources: ${missingResources.join(', ')}`
        };
    },
    
    // 测试DataTable列数问题
    testDataTableColumns() {
        const tables = document.querySelectorAll('.data-table');
        let columnsCorrect = true;
        let errorDetails = [];
        
        tables.forEach((table, index) => {
            const headers = table.querySelectorAll('thead th');
            const firstRow = table.querySelector('tbody tr');
            
            if (firstRow) {
                const cells = firstRow.querySelectorAll('td');
                if (headers.length !== cells.length) {
                    columnsCorrect = false;
                    errorDetails.push(`Table ${index + 1}: ${headers.length} headers vs ${cells.length} cells`);
                }
            }
        });
        
        return {
            passed: columnsCorrect,
            message: columnsCorrect ? 
                `All ${tables.length} tables have correct column counts` : 
                `Column mismatch in: ${errorDetails.join(', ')}`
        };
    },
    
    // 测试图表库加载
    async testChartLibraryLoading() {
        try {
            if (typeof loadChartSupport === 'function') {
                const result = await loadChartSupport();
                const chartStatus = typeof getChartStatus === 'function' ? getChartStatus() : null;
                
                return {
                    passed: result && typeof Chart !== 'undefined',
                    message: result ? 
                        `Chart library loaded successfully, version: ${chartStatus?.version || 'unknown'}` : 
                        'Chart library loading failed'
                };
            } else {
                return {
                    passed: false,
                    message: 'loadChartSupport function not available'
                };
            }
        } catch (error) {
            return {
                passed: false,
                message: `Chart loading test failed: ${error.message}`
            };
        }
    },
    
    // 测试AssetLoader更新
    testAssetLoaderUpdates() {
        const hasAssetLoader = typeof window.AssetLoader !== 'undefined';
        const hasChartStatus = typeof window.getChartStatus === 'function';
        const hasWaitForReady = typeof window.waitForChartReady === 'function';
        
        let improvements = [];
        if (hasAssetLoader) improvements.push('AssetLoader available');
        if (hasChartStatus) improvements.push('Chart status checking');
        if (hasWaitForReady) improvements.push('Chart ready waiting');
        
        return {
            passed: hasAssetLoader && hasChartStatus && hasWaitForReady,
            message: improvements.length > 0 ? 
                `AssetLoader improvements: ${improvements.join(', ')}` : 
                'AssetLoader not properly updated'
        };
    },
    
    // 测试定时器重复启动问题
    testNoTimerDuplication() {
        const hasApiManager = typeof window.apiStatusManager !== 'undefined';
        
        if (!hasApiManager) {
            return {
                passed: true,
                message: 'API状态管理器不存在，跳过测试'
            };
        }
        
        // 检查是否有防重复启动逻辑
        const hasTimerControl = window.apiStatusManager.checkInterval !== undefined;
        
        return {
            passed: hasTimerControl,
            message: hasTimerControl ? 
                'API状态管理器具有定时器控制功能' : 
                'API状态管理器缺少定时器控制'
        };
    },
    
    // 测试预加载警告问题
    testPreloadWarnings() {
        // 检查预加载元素
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        let correctCrossOrigin = true;
        let details = [];
        
        preloadLinks.forEach(link => {
            const src = link.href;
            const hasCrossOrigin = link.hasAttribute('crossorigin');
            const isSameOrigin = src.startsWith(window.location.origin) || src.startsWith('/');
            
            if (isSameOrigin && hasCrossOrigin) {
                correctCrossOrigin = false;
                details.push(`同源资源 ${src} 不应有crossorigin属性`);
            } else if (!isSameOrigin && !hasCrossOrigin) {
                correctCrossOrigin = false;
                details.push(`跨源资源 ${src} 应有crossorigin属性`);
            }
        });
        
        return {
            passed: correctCrossOrigin,
            message: correctCrossOrigin ? 
                `${preloadLinks.length} 个预加载资源的跨域属性设置正确` : 
                `预加载跨域属性问题: ${details.join(', ')}`
        };
    },
    
    // 打印测试总结
    printSummary() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.passed).length;
        const failed = total - passed;
        
        console.log('\n=== 修复效果验证总结 ===');
        console.log(`总测试数: ${total}`);
        console.log(`通过: ${passed}`);
        console.log(`失败: ${failed}`);
        console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (failed > 0) {
            console.log('\n失败的测试:');
            this.results.filter(r => !r.passed).forEach(result => {
                console.log(`❌ ${result.test}: ${result.message}`);
            });
        }
        
        if (passed === total) {
            console.log('\n🎉 所有测试通过！修复效果良好。');
        }
    }
};

// 添加到全局对象
if (typeof window !== 'undefined') {
    window.FixValidationTests = FixValidationTests;
}

console.log('修复效果验证脚本已加载，使用 FixValidationTests.runAllTests() 来运行测试');
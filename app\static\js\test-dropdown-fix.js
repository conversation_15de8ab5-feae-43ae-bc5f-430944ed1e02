/**
 * 下拉菜单修复效果测试脚本
 */

const DropdownFixTests = {
    results: [],
    
    async runAllTests() {
        console.log('=== 开始测试下拉菜单修复效果 ===');
        
        const tests = [
            'testBootstrapDropdownAvailable',
            'testDropdownHtmlStructure',
            'testDropdownInitialization',
            'testViewSwitchFunction',
            'testNoManualEventConflicts',
            'testAccessibilityAttributes'
        ];
        
        for (const testName of tests) {
            try {
                const result = await this[testName]();
                this.results.push({
                    test: testName,
                    passed: result.passed,
                    message: result.message,
                    details: result.details
                });
                console.log(`${result.passed ? '✅' : '❌'} ${testName}: ${result.message}`);
            } catch (error) {
                this.results.push({
                    test: testName,
                    passed: false,
                    message: `测试执行失败: ${error.message}`
                });
                console.error(`❌ ${testName}: ${error.message}`);
            }
        }
        
        this.printSummary();
        return this.results;
    },
    
    // 测试Bootstrap下拉菜单是否可用
    testBootstrapDropdownAvailable() {
        const hasBootstrap = typeof bootstrap !== 'undefined';
        const hasDropdown = hasBootstrap && typeof bootstrap.Dropdown !== 'undefined';
        
        return {
            passed: hasDropdown,
            message: hasDropdown ? 
                'Bootstrap下拉菜单组件可用' : 
                'Bootstrap下拉菜单组件不可用',
            details: hasBootstrap ? 'Bootstrap已加载' : 'Bootstrap未加载'
        };
    },
    
    // 测试下拉菜单HTML结构
    testDropdownHtmlStructure() {
        const dropdown = document.getElementById('dataViewSelector');
        const dropdownMenu = document.querySelector('.dropdown-menu.view-selector');
        const menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
        
        const hasToggle = dropdown && dropdown.hasAttribute('data-bs-toggle');
        const hasMenu = dropdownMenu !== null;
        const hasItems = menuItems.length > 0;
        const hasAriaAttributes = dropdown && dropdown.hasAttribute('aria-expanded') && 
                                 dropdownMenu && dropdownMenu.hasAttribute('aria-labelledby');
        
        const allCorrect = hasToggle && hasMenu && hasItems && hasAriaAttributes;
        
        return {
            passed: allCorrect,
            message: allCorrect ? 
                `下拉菜单HTML结构正确 (${menuItems.length}个菜单项)` : 
                '下拉菜单HTML结构有问题',
            details: `Toggle: ${hasToggle}, Menu: ${hasMenu}, Items: ${hasItems}, Aria: ${hasAriaAttributes}`
        };
    },
    
    // 测试下拉菜单初始化
    testDropdownInitialization() {
        const dropdown = document.getElementById('dataViewSelector');
        if (!dropdown) {
            return {
                passed: false,
                message: '下拉菜单按钮未找到'
            };
        }
        
        // 检查Bootstrap是否已为此元素创建了下拉菜单实例
        let hasInstance = false;
        try {
            const instance = bootstrap.Dropdown.getInstance(dropdown);
            hasInstance = instance !== null;
        } catch (error) {
            hasInstance = false;
        }
        
        return {
            passed: hasInstance,
            message: hasInstance ? 
                'Bootstrap下拉菜单实例已创建' : 
                'Bootstrap下拉菜单实例未创建'
        };
    },
    
    // 测试视图切换函数
    testViewSwitchFunction() {
        const hasSwitchFunction = typeof switchDataView === 'function';
        const menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
        
        let hasDataViewAttributes = true;
        menuItems.forEach(item => {
            if (!item.hasAttribute('data-view')) {
                hasDataViewAttributes = false;
            }
        });
        
        return {
            passed: hasSwitchFunction && hasDataViewAttributes,
            message: hasSwitchFunction && hasDataViewAttributes ? 
                '视图切换功能配置正确' : 
                '视图切换功能配置有问题',
            details: `Function: ${hasSwitchFunction}, Attributes: ${hasDataViewAttributes}`
        };
    },
    
    // 测试是否移除了手动事件冲突
    testNoManualEventConflicts() {
        // 检查是否还有手动的样式覆盖
        const manualStyles = document.querySelector('style');
        let hasManualDropdownStyles = false;
        
        if (manualStyles) {
            const styleContent = manualStyles.textContent || '';
            hasManualDropdownStyles = styleContent.includes('.dropdown-menu.show') && 
                                    styleContent.includes('display: block !important');
        }
        
        // 检查是否有手动的事件阻止
        const dropdown = document.getElementById('dataViewSelector');
        let hasConflictingEvents = false;
        
        if (dropdown) {
            // 简单检查：尝试触发Bootstrap事件看是否被阻止
            try {
                const event = new Event('click', { bubbles: true });
                dropdown.dispatchEvent(event);
                hasConflictingEvents = false; // 如果没有错误，说明没有冲突
            } catch (error) {
                hasConflictingEvents = true;
            }
        }
        
        const noConflicts = !hasManualDropdownStyles && !hasConflictingEvents;
        
        return {
            passed: noConflicts,
            message: noConflicts ? 
                '没有检测到手动事件冲突' : 
                '检测到可能的手动事件冲突',
            details: `Manual styles: ${hasManualDropdownStyles}, Event conflicts: ${hasConflictingEvents}`
        };
    },
    
    // 测试可访问性属性
    testAccessibilityAttributes() {
        const dropdown = document.getElementById('dataViewSelector');
        const dropdownMenu = document.querySelector('.dropdown-menu.view-selector');
        
        const hasAriaExpanded = dropdown && dropdown.hasAttribute('aria-expanded');
        const hasAriaLabelledBy = dropdownMenu && dropdownMenu.hasAttribute('aria-labelledby');
        const correctLabelReference = hasAriaLabelledBy && 
                                    dropdownMenu.getAttribute('aria-labelledby') === 'dataViewSelector';
        
        const allAccessible = hasAriaExpanded && hasAriaLabelledBy && correctLabelReference;
        
        return {
            passed: allAccessible,
            message: allAccessible ? 
                '可访问性属性配置正确' : 
                '可访问性属性配置不完整',
            details: `aria-expanded: ${hasAriaExpanded}, aria-labelledby: ${hasAriaLabelledBy}, correct reference: ${correctLabelReference}`
        };
    },
    
    // 打印测试总结
    printSummary() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.passed).length;
        const failed = total - passed;
        
        console.log('\n=== 下拉菜单修复测试总结 ===');
        console.log(`总测试数: ${total}`);
        console.log(`通过: ${passed}`);
        console.log(`失败: ${failed}`);
        console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (failed > 0) {
            console.log('\n失败的测试:');
            this.results.filter(r => !r.passed).forEach(result => {
                console.log(`❌ ${result.test}: ${result.message}`);
                if (result.details) {
                    console.log(`   详情: ${result.details}`);
                }
            });
        }
        
        if (passed === total) {
            console.log('\n🎉 所有测试通过！下拉菜单修复成功。');
        } else if (passed >= total * 0.8) {
            console.log('\n✅ 大部分测试通过，下拉菜单基本正常。');
        } else {
            console.log('\n⚠️ 多个测试失败，下拉菜单可能仍有问题。');
        }
    },
    
    // 手动测试下拉菜单功能
    async manualDropdownTest() {
        console.log('开始手动下拉菜单测试...');
        
        const dropdown = document.getElementById('dataViewSelector');
        if (!dropdown) {
            console.error('下拉菜单按钮未找到');
            return false;
        }
        
        try {
            // 模拟点击下拉菜单
            dropdown.click();
            
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待动画
            
            const dropdownMenu = document.querySelector('.dropdown-menu.view-selector');
            const isVisible = dropdownMenu && dropdownMenu.classList.contains('show');
            
            if (isVisible) {
                console.log('✅ 下拉菜单成功打开');
                
                // 测试菜单项点击
                const firstItem = dropdownMenu.querySelector('.dropdown-item');
                if (firstItem) {
                    firstItem.click();
                    console.log('✅ 菜单项点击测试成功');
                }
                
                return true;
            } else {
                console.error('❌ 下拉菜单未能打开');
                return false;
            }
        } catch (error) {
            console.error('❌ 手动测试失败:', error);
            return false;
        }
    }
};

// 添加到全局对象
if (typeof window !== 'undefined') {
    window.DropdownFixTests = DropdownFixTests;
}

console.log('下拉菜单修复测试脚本已加载，使用 DropdownFixTests.runAllTests() 来运行测试');
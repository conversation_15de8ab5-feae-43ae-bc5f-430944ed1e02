# ===== 单阶段构建 Dockerfile =====
# 此文件适用于网络连接受限的环境
# 需要预先在本地构建前端资源

FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    libffi-dev \
    libpng-dev \
    libtiff5-dev \
    libopenjp2-7-dev \
    fonts-dejavu-core \
    fonts-liberation \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级pip和安装基础工具
RUN pip install --upgrade pip setuptools wheel

# 先安装numpy，确保版本兼容性
RUN pip install --no-cache-dir numpy==1.25.2

# 拷贝Python依赖文件并安装
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 拷贝应用源码（包括预构建的前端资源）
COPY . .

# 创建必要的目录
RUN mkdir -p logs cache && \
    chmod 755 logs cache

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/ || exit 1

# 启动命令
CMD ["gunicorn", "-c", "gunicorn_config.py", "run:app"]
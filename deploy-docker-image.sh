#!/bin/bash

# HDSC查询应用 - Docker镜像快速部署脚本
# 版本: v1.0
# 用途: 在目标服务器上快速部署Docker镜像

set -e

# 配置变量
IMAGE_FILE="hdsc-query-app-v1.0.tar"
IMAGE_NAME="hdsc-query-app:v1.0"
CONTAINER_NAME="hdsc-query-app"
HOST_PORT="5000"
CONTAINER_PORT="5000"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查镜像文件
check_image_file() {
    log_info "检查镜像文件..."
    if [ ! -f "$IMAGE_FILE" ]; then
        log_error "镜像文件 $IMAGE_FILE 不存在"
        log_info "请确保镜像文件在当前目录下"
        exit 1
    fi
    
    local file_size=$(du -h "$IMAGE_FILE" | cut -f1)
    log_success "找到镜像文件: $IMAGE_FILE (大小: $file_size)"
}

# 停止并删除现有容器
cleanup_existing() {
    log_info "清理现有容器..."
    
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_warning "停止现有容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        log_warning "删除现有容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
    fi
    
    log_success "容器清理完成"
}

# 导入Docker镜像
import_image() {
    log_info "导入Docker镜像..."
    
    # 检查镜像是否已存在
    if docker images -q "$IMAGE_NAME" | grep -q .; then
        log_warning "镜像 $IMAGE_NAME 已存在，将删除旧镜像"
        docker rmi "$IMAGE_NAME" || true
    fi
    
    log_info "正在导入镜像文件，请稍候..."
    docker load -i "$IMAGE_FILE"
    
    log_success "镜像导入完成"
}

# 运行容器
run_container() {
    log_info "启动应用容器..."
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":$HOST_PORT "; then
        log_warning "端口 $HOST_PORT 已被占用，请检查或修改端口配置"
    fi
    
    # 创建日志目录
    mkdir -p ./logs
    
    # 运行容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$HOST_PORT:$CONTAINER_PORT" \
        -v "$(pwd)/logs:/app/logs" \
        -e FLASK_ENV=production \
        --restart unless-stopped \
        "$IMAGE_NAME"
    
    log_success "容器启动成功"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 等待容器启动
    sleep 5
    
    # 检查容器状态
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "容器启动失败"
        log_info "查看容器日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
    
    # 检查应用响应
    log_info "等待应用启动..."
    for i in {1..30}; do
        if curl -s "http://localhost:$HOST_PORT/health" &> /dev/null; then
            log_success "应用健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_warning "健康检查超时，但容器正在运行"
            log_info "请手动检查: http://localhost:$HOST_PORT"
        fi
        
        sleep 2
    done
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=========================================="
    echo "🎉 HDSC查询应用部署完成!"
    echo "=========================================="
    echo "📋 部署信息:"
    echo "   - 容器名称: $CONTAINER_NAME"
    echo "   - 镜像版本: $IMAGE_NAME"
    echo "   - 访问地址: http://localhost:$HOST_PORT"
    echo "   - 健康检查: http://localhost:$HOST_PORT/health"
    echo ""
    echo "🔧 管理命令:"
    echo "   - 查看状态: docker ps"
    echo "   - 查看日志: docker logs $CONTAINER_NAME"
    echo "   - 停止应用: docker stop $CONTAINER_NAME"
    echo "   - 重启应用: docker restart $CONTAINER_NAME"
    echo ""
    echo "📁 日志文件: ./logs/"
    echo "=========================================="
}

# 主函数
main() {
    echo "🚀 HDSC查询应用 - Docker快速部署脚本"
    echo "版本: v1.0"
    echo ""
    
    check_docker
    check_image_file
    cleanup_existing
    import_image
    run_container
    verify_deployment
    show_deployment_info
    
    log_success "部署完成! 🎉"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查上述日志"; exit 1' ERR

# 执行主函数
main "$@"
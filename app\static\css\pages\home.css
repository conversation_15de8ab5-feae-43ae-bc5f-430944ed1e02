/* ==========================================
   Home Page Styles - 首页样式
   ========================================== */

/* 小工具卡片样式 */
.icon-box {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.bg-info-light {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.bg-secondary-light {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.bg-purple-light {
    background-color: rgba(138, 43, 226, 0.1);
    color: #8a2be2;
}

/* 快速链接样式 */
.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #495057;
    padding: 10px;
    border-radius: 10px;
    transition: all 0.3s;
}

.quick-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    text-decoration: none;
    color: #495057;
}

.quick-link-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 8px;
}

/* 通知列表样式 */
.notice-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.notice-list .list-group-item {
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
}

.notice-list .list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
}

.smaller {
    font-size: 0.7rem;
}

/* 简化版迷你日历样式 */
.mini-calendar {
    height: 300px;
    background-color: #fff;
    border-radius: 8px;
}

.simple-calendar {
    padding: 10px;
}

.calendar-grid {
    font-size: 12px;
}

.week-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 5px;
}

.day-header {
    text-align: center;
    font-weight: bold;
    padding: 5px;
    color: #6c757d;
    font-size: 11px;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 11px;
}

.calendar-day:hover:not(.empty) {
    background-color: #f8f9fa;
}

.calendar-day.today {
    background-color: #0d6efd;
    color: white;
    font-weight: bold;
}

.calendar-day.empty {
    cursor: default;
}

.calendar-day.has-event .day-number {
    font-weight: bold;
}

.event-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
}

/* 待办事项样式 */
.todo-list .list-group-item {
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.todo-list .list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #198754;
}

.todo-list .list-group-item.completed {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.form-check-input:checked + .todo-content h6 {
    text-decoration: line-through;
    color: #6c757d;
}

/* 计算器样式 */
.calculator {
    max-width: 300px;
    margin: 0 auto;
}

#calcDisplay {
    font-size: 1.2rem;
    font-weight: bold;
    height: 60px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    text-align: right;
    padding: 5px 15px;
    font-family: 'Courier New', monospace;
    line-height: 1.2;
    resize: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.calc-buttons button {
    height: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
}

.calc-buttons .btn-light {
    background-color: #f8f9fa;
    color: #333;
}

.calc-buttons .btn-light:hover {
    background-color: #e9ecef;
    transform: scale(1.05);
}

.calc-buttons .btn-warning {
    background-color: #ff9500;
    border-color: #ff9500;
    color: white;
}

.calc-buttons .btn-warning:hover {
    background-color: #e8890b;
    border-color: #e8890b;
    transform: scale(1.05);
}

.calc-buttons .btn-warning:active {
    background-color: #d1780a;
    border-color: #d1780a;
}

/* 数字0按钮特殊样式 */
.calc-buttons .row:last-child .col-6 button {
    border-radius: 25px;
}

/* 计算器模态框样式优化 */
#calculatorModal .modal-content {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

#calculatorModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

#calculatorModal .modal-header .btn-close {
    filter: brightness(0) invert(1);
}

/* 按钮按下效果 */
.calc-buttons button:active {
    transform: scale(0.95);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .icon-box {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .quick-link-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .quick-link {
        padding: 8px;
    }
    
    .mini-calendar {
        height: 250px;
    }
    
    .calendar-day {
        height: 25px;
        font-size: 10px;
    }
}

/* 加载状态和错误状态样式 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-state {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

/* 可访问性增强 */
.quick-link:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.calendar-day:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 1px;
}

/* 性能优化 - 减少重绘 */
.card {
    will-change: transform;
    backface-visibility: hidden;
}

.quick-link {
    will-change: transform;
    backface-visibility: hidden;
}
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页布局对比测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="app/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="app/static/vendor/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="app/static/vendor/datatables/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="app/static/vendor/datatables/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <!-- 统一分页样式 -->
    <link rel="stylesheet" href="app/static/css/unified-pagination.css">
    
    <style>
        body { padding: 20px; }
        .comparison-section { margin-bottom: 50px; }
        .layout-demo {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .layout-demo.old-layout {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.05);
        }
        .layout-demo.new-layout {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.05);
        }
        .layout-title {
            margin-bottom: 15px;
            font-weight: 600;
        }
        .old-layout .layout-title {
            color: #dc3545;
        }
        .new-layout .layout-title {
            color: #28a745;
        }
        .screen-width-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        /* 模拟旧布局的样式 */
        .old-layout .dataTables_wrapper .dataTables_info {
            display: inline-block;
            float: left;
            margin-top: 8px;
        }
        .old-layout .dataTables_wrapper .dataTables_paginate {
            display: inline-block;
            float: right;
            margin-top: 0;
        }
        .old-layout .dataTables_wrapper .dataTables_length,
        .old-layout .dataTables_wrapper .dataTables_filter {
            display: inline-block;
        }
        
        /* 强制清除浮动 */
        .old-layout .dataTables_wrapper::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="screen-width-indicator">
        <i class="bi bi-display"></i> 屏幕宽度: <span id="screenWidth"></span>px
    </div>
    
    <div class="container-fluid">
        <h1 class="mb-4">
            <i class="bi bi-layout-three-columns"></i> 
            分页布局优化对比
        </h1>
        <p class="lead text-muted">对比优化前后在窄容器（col-md-6）中的分页布局效果</p>
        
        <!-- 旧布局演示 -->
        <div class="comparison-section">
            <div class="layout-demo old-layout">
                <h3 class="layout-title">
                    <i class="bi bi-x-circle"></i> 
                    优化前：分页和信息文本在同一行（空间不足）
                </h3>
                <p class="text-danger small mb-3">
                    问题：在窄容器中，分页按钮和信息文本挤在同一行，导致分页按钮显示不全
                </p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-striped" id="oldLayoutTable">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>月份</th>
                                        <th>电商订单</th>
                                        <th>租赁订单</th>
                                        <th>总订单数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td></td><td>2024-01</td><td>1,234</td><td>567</td><td>1,801</td></tr>
                                    <tr><td></td><td>2024-02</td><td>1,456</td><td>678</td><td>2,134</td></tr>
                                    <tr><td></td><td>2024-03</td><td>1,678</td><td>789</td><td>2,467</td></tr>
                                    <tr><td></td><td>2024-04</td><td>1,890</td><td>890</td><td>2,780</td></tr>
                                    <tr><td></td><td>2024-05</td><td>2,012</td><td>901</td><td>2,913</td></tr>
                                    <tr><td></td><td>2024-06</td><td>2,234</td><td>1,012</td><td>3,246</td></tr>
                                    <tr><td></td><td>2024-07</td><td>2,456</td><td>1,123</td><td>3,579</td></tr>
                                    <tr><td></td><td>2024-08</td><td>2,678</td><td>1,234</td><td>3,912</td></tr>
                                    <tr><td></td><td>2024-09</td><td>2,890</td><td>1,345</td><td>4,235</td></tr>
                                    <tr><td></td><td>2024-10</td><td>3,012</td><td>1,456</td><td>4,468</td></tr>
                                    <tr><td></td><td>2024-11</td><td>3,234</td><td>1,567</td><td>4,801</td></tr>
                                    <tr><td></td><td>2024-12</td><td>3,456</td><td>1,678</td><td>5,134</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新布局演示 -->
        <div class="comparison-section">
            <div class="layout-demo new-layout">
                <h3 class="layout-title">
                    <i class="bi bi-check-circle"></i> 
                    优化后：分页独占一行，信息文本在下方（空间充足）
                </h3>
                <p class="text-success small mb-3">
                    优势：分页控件独占一行有充足空间，信息文本在下方独立显示，布局清晰美观
                </p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-striped data-table" id="newLayoutTable">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>月份</th>
                                        <th>电商订单</th>
                                        <th>租赁订单</th>
                                        <th>总订单数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td></td><td>2024-01</td><td>1,234</td><td>567</td><td>1,801</td></tr>
                                    <tr><td></td><td>2024-02</td><td>1,456</td><td>678</td><td>2,134</td></tr>
                                    <tr><td></td><td>2024-03</td><td>1,678</td><td>789</td><td>2,467</td></tr>
                                    <tr><td></td><td>2024-04</td><td>1,890</td><td>890</td><td>2,780</td></tr>
                                    <tr><td></td><td>2024-05</td><td>2,012</td><td>901</td><td>2,913</td></tr>
                                    <tr><td></td><td>2024-06</td><td>2,234</td><td>1,012</td><td>3,246</td></tr>
                                    <tr><td></td><td>2024-07</td><td>2,456</td><td>1,123</td><td>3,579</td></tr>
                                    <tr><td></td><td>2024-08</td><td>2,678</td><td>1,234</td><td>3,912</td></tr>
                                    <tr><td></td><td>2024-09</td><td>2,890</td><td>1,345</td><td>4,235</td></tr>
                                    <tr><td></td><td>2024-10</td><td>3,012</td><td>1,456</td><td>4,468</td></tr>
                                    <tr><td></td><td>2024-11</td><td>3,234</td><td>1,567</td><td>4,801</td></tr>
                                    <tr><td></td><td>2024-12</td><td>3,456</td><td>1,678</td><td>5,134</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试说明 -->
        <div class="comparison-section">
            <div class="alert alert-info">
                <h5><i class="bi bi-lightbulb"></i> 测试说明</h5>
                <ul class="mb-0">
                    <li><strong>调整浏览器窗口大小</strong>：观察两种布局在不同宽度下的表现</li>
                    <li><strong>移动端测试</strong>：使用开发者工具模拟移动设备</li>
                    <li><strong>关键改进</strong>：新布局确保分页按钮在窄容器中也能完整显示</li>
                    <li><strong>用户体验</strong>：信息文本独立显示，不会与分页控件产生布局冲突</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="app/static/vendor/jquery/jquery.min.js"></script>
    <script src="app/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/vendor/datatables/js/jquery.dataTables.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.bootstrap5.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.responsive.min.js"></script>
    <script src="app/static/vendor/datatables/js/responsive.bootstrap5.min.js"></script>
    
    <!-- 统一分页管理器 -->
    <script src="app/static/js/unified-pagination-manager.js"></script>
    
    <script>
        // 更新屏幕宽度显示
        function updateScreenWidth() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }
        
        // 初始化
        $(document).ready(function() {
            updateScreenWidth();
            window.addEventListener('resize', updateScreenWidth);
            
            const isMobile = window.innerWidth <= 768;
            
            // 旧布局表格 - 使用传统的DOM结构
            $('#oldLayoutTable').DataTable({
                responsive: {
                    details: {
                        type: 'column',
                        target: 0
                    }
                },
                columnDefs: [
                    {
                        className: 'dtr-control',
                        orderable: false,
                        targets: 0,
                        width: "40px"
                    }
                ],
                autoWidth: false,
                paging: true,
                pageLength: 5,
                lengthMenu: [[5, 10, 25, -1], [5, 10, 25, "全部"]],
                order: [],
                language: {
                    search: "搜索:",
                    lengthMenu: "显示 _MENU_ 条记录",
                    zeroRecords: "无匹配数据",
                    info: "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",
                    infoEmpty: "显示第 0 至 0 条记录，共 0 条",
                    infoFiltered: "(从 _MAX_ 条记录中筛选)",
                    emptyTable: "表中数据为空",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    }
                },
                // 旧的DOM结构 - 分页和信息在同一行
                dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-6"i><"col-sm-6"p>>'
            });
            
            // 新布局表格 - 使用优化后的配置
            $('#newLayoutTable').DataTable(window.getSummaryPagePaginationConfig(isMobile, true));
            
            console.log('布局对比测试页面初始化完成');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧凑分页模式演示</title>
    
    <!-- Bootstrap CSS -->
    <link href="app/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="app/static/vendor/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="app/static/vendor/datatables/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="app/static/vendor/datatables/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <!-- 统一分页样式 -->
    <link rel="stylesheet" href="app/static/css/unified-pagination.css">
    
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .demo-section { 
            margin-bottom: 40px; 
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .mode-indicator {
            position: absolute;
            top: 10px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .normal-mode { background: #17a2b8; color: white; }
        .compact-mode { background: #28a745; color: white; }
        .space-saved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-arrows-collapse"></i> 
                    紧凑分页模式演示
                </h1>
                <p class="lead text-muted">对比标准模式和紧凑模式的空间利用效果</p>
            </div>
        </div>
        
        <div class="row">
            <!-- 标准模式 -->
            <div class="col-md-6">
                <div class="demo-section position-relative">
                    <span class="mode-indicator normal-mode">标准模式</span>
                    <h4 class="text-primary">
                        <i class="bi bi-table"></i> 
                        标准分页布局
                    </h4>
                    <p class="text-muted small">常规间距和尺寸</p>
                    
                    <div class="table-responsive">
                        <table class="table table-striped" id="normalTable">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>月份</th>
                                    <th>订单数</th>
                                    <th>金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td></td><td>2024-01</td><td>1,234</td><td>¥123,456</td></tr>
                                <tr><td></td><td>2024-02</td><td>1,456</td><td>¥145,678</td></tr>
                                <tr><td></td><td>2024-03</td><td>1,678</td><td>¥167,890</td></tr>
                                <tr><td></td><td>2024-04</td><td>1,890</td><td>¥189,012</td></tr>
                                <tr><td></td><td>2024-05</td><td>2,012</td><td>¥201,234</td></tr>
                                <tr><td></td><td>2024-06</td><td>2,234</td><td>¥223,456</td></tr>
                                <tr><td></td><td>2024-07</td><td>2,456</td><td>¥245,678</td></tr>
                                <tr><td></td><td>2024-08</td><td>2,678</td><td>¥267,890</td></tr>
                                <tr><td></td><td>2024-09</td><td>2,890</td><td>¥289,012</td></tr>
                                <tr><td></td><td>2024-10</td><td>3,012</td><td>¥301,234</td></tr>
                                <tr><td></td><td>2024-11</td><td>3,234</td><td>¥323,456</td></tr>
                                <tr><td></td><td>2024-12</td><td>3,456</td><td>¥345,678</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 紧凑模式 -->
            <div class="col-md-6">
                <div class="demo-section position-relative">
                    <span class="mode-indicator compact-mode">紧凑模式</span>
                    <h4 class="text-success">
                        <i class="bi bi-arrows-collapse"></i> 
                        紧凑分页布局
                    </h4>
                    <p class="text-muted small">减少间距，最大化内容空间</p>
                    
                    <div class="table-responsive">
                        <table class="table table-striped data-table" id="compactTable">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>月份</th>
                                    <th>订单数</th>
                                    <th>金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td></td><td>2024-01</td><td>1,234</td><td>¥123,456</td></tr>
                                <tr><td></td><td>2024-02</td><td>1,456</td><td>¥145,678</td></tr>
                                <tr><td></td><td>2024-03</td><td>1,678</td><td>¥167,890</td></tr>
                                <tr><td></td><td>2024-04</td><td>1,890</td><td>¥189,012</td></tr>
                                <tr><td></td><td>2024-05</td><td>2,012</td><td>¥201,234</td></tr>
                                <tr><td></td><td>2024-06</td><td>2,234</td><td>¥223,456</td></tr>
                                <tr><td></td><td>2024-07</td><td>2,456</td><td>¥245,678</td></tr>
                                <tr><td></td><td>2024-08</td><td>2,678</td><td>¥267,890</td></tr>
                                <tr><td></td><td>2024-09</td><td>2,890</td><td>¥289,012</td></tr>
                                <tr><td></td><td>2024-10</td><td>3,012</td><td>¥301,234</td></tr>
                                <tr><td></td><td>2024-11</td><td>3,234</td><td>¥323,456</td></tr>
                                <tr><td></td><td>2024-12</td><td>3,456</td><td>¥345,678</td></tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="space-saved">
                        <i class="bi bi-check-circle"></i> 
                        节省约30%的垂直空间
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="demo-section">
                    <h4><i class="bi bi-gear"></i> 控制面板</h4>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleMode('normalTable')">
                            切换标准表格模式
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="toggleMode('compactTable')">
                            切换紧凑表格模式
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="compareHeights()">
                            对比高度差异
                        </button>
                    </div>
                    
                    <div class="mt-3">
                        <h6>紧凑模式优势：</h6>
                        <ul class="small text-muted">
                            <li>分页按钮更小巧，节省水平空间</li>
                            <li>信息文本字体更小，减少垂直占用</li>
                            <li>边距和内边距大幅减少</li>
                            <li>在有限空间内展示更多内容</li>
                            <li>特别适合移动端和窄容器场景</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="app/static/vendor/jquery/jquery.min.js"></script>
    <script src="app/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/vendor/datatables/js/jquery.dataTables.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.bootstrap5.min.js"></script>
    <script src="app/static/vendor/datatables/js/dataTables.responsive.min.js"></script>
    <script src="app/static/vendor/datatables/js/responsive.bootstrap5.min.js"></script>
    
    <!-- 统一分页管理器 -->
    <script src="app/static/js/unified-pagination-manager.js"></script>
    
    <script>
        // 切换模式
        function toggleMode(tableId) {
            const table = document.getElementById(tableId);
            const wrapper = table.closest('.dataTables_wrapper');
            
            if (wrapper.classList.contains('compact-mode')) {
                window.disableCompactPagination(table);
                updateModeIndicator(tableId, false);
            } else {
                window.enableCompactPagination(table);
                updateModeIndicator(tableId, true);
            }
        }
        
        // 更新模式指示器
        function updateModeIndicator(tableId, isCompact) {
            const section = document.getElementById(tableId).closest('.demo-section');
            const indicator = section.querySelector('.mode-indicator');
            
            if (isCompact) {
                indicator.className = 'mode-indicator compact-mode';
                indicator.textContent = '紧凑模式';
            } else {
                indicator.className = 'mode-indicator normal-mode';
                indicator.textContent = '标准模式';
            }
        }
        
        // 对比高度差异
        function compareHeights() {
            const normalWrapper = document.getElementById('normalTable').closest('.dataTables_wrapper');
            const compactWrapper = document.getElementById('compactTable').closest('.dataTables_wrapper');
            
            const normalHeight = normalWrapper.offsetHeight;
            const compactHeight = compactWrapper.offsetHeight;
            const saved = normalHeight - compactHeight;
            const percentage = ((saved / normalHeight) * 100).toFixed(1);
            
            alert(`高度对比：\n标准模式：${normalHeight}px\n紧凑模式：${compactHeight}px\n节省空间：${saved}px (${percentage}%)`);
        }
        
        // 初始化
        $(document).ready(function() {
            const isMobile = window.innerWidth <= 768;
            
            // 标准模式表格
            $('#normalTable').DataTable(window.getSummaryPagePaginationConfig(isMobile, true));
            
            // 紧凑模式表格
            const compactTable = $('#compactTable').DataTable(window.getSummaryPagePaginationConfig(isMobile, true));
            
            // 延迟启用紧凑模式，确保表格完全初始化
            setTimeout(() => {
                window.enableCompactPagination('#compactTable');
            }, 100);
            
            console.log('紧凑分页演示页面初始化完成');
        });
    </script>
</body>
</html>

# HDSC Query App 生产部署指南

## 前置要求
- 目标服务器已安装 Docker 和 Docker Compose
- 确保服务器有足够的磁盘空间（建议至少 2GB 空闲空间）
- 推荐配置Docker国内镜像源以提高构建和拉取速度

## 部署步骤

### 1. 镜像导出（在Windows开发机器上）

1. **启动 Docker Desktop**
   - 确保 Docker Desktop 正在运行

2. **运行构建和导出脚本**
   ```batch
   # Windows
   build-and-export.bat
   
   # Linux/macOS
   chmod +x build-and-export.sh
   ./build-and-export.sh
   ```

### 2. 文件传输到生产服务器

将以下文件传输到生产服务器：
```
hdsc-query-app-production.tar    # Docker镜像文件
docker-compose.yml               # Docker Compose配置
AHT/                            # 文档目录（如果存在）
BHT/                            # 文档目录（如果存在）
HZ/                             # 文档目录（如果存在）
config.py                       # 配置文件
gunicorn_config.py              # Gunicorn配置
```

### 3. 配置Docker镜像源（可选但推荐）

在生产服务器上配置Docker国内镜像源：

1. **Linux系统**：
   ```bash
   # 创建或编辑Docker配置文件
   sudo mkdir -p /etc/docker
   sudo tee /etc/docker/daemon.json <<-'EOF'
   {
     "registry-mirrors": [
       "https://registry.cn-hangzhou.aliyuncs.com",
       "https://hub-mirror.c.163.com",
       "https://mirror.baidubce.com"
     ]
   }
   EOF
   
   # 重启Docker服务
   sudo systemctl daemon-reload
   sudo systemctl restart docker
   ```

2. **Windows系统**：
   - 右键点击Docker Desktop图标 → Settings
   - 选择 Docker Engine
   - 将上述JSON配置添加到配置文件中
   - 点击 Apply & Restart

### 4. 生产服务器部署

1. **加载Docker镜像**
   ```bash
   docker load -i hdsc-query-app-production.tar
   ```

2. **验证镜像加载**
   ```bash
   docker images | grep hdsc-query-app
   ```

3. **创建必要的目录**
   ```bash
   mkdir -p logs cache
   chmod 755 logs cache
   ```

4. **启动服务**
   ```bash
   docker-compose up -d
   ```

5. **检查服务状态**
   ```bash
   docker-compose ps
   docker-compose logs -f hdsc-query-app
   ```

### 5. 验证部署

1. **健康检查**
   ```bash
   curl -f http://localhost:5000/
   ```

2. **查看日志**
   ```bash
   docker-compose logs hdsc-query-app
   tail -f logs/app.log
   ```

### 6. 服务管理命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 更新服务（当有新镜像时）
docker-compose down
docker load -i hdsc-query-app-production-new.tar
docker-compose up -d
```

## 配置说明

### 端口配置
- 默认端口：5000
- 可在 `docker-compose.yml` 中修改端口映射

### 数据持久化
- 日志目录：`./logs` 挂载到容器的 `/app/logs`
- 缓存目录：`./cache` 挂载到容器的 `/app/cache`
- 配置文件：以只读方式挂载

### 环境变量
- `FLASK_ENV=production`：生产环境
- `TZ=Asia/Shanghai`：时区设置

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :5000
   # 修改 docker-compose.yml 中的端口映射
   ```

2. **权限问题**
   ```bash
   # 确保日志和缓存目录有正确权限
   chmod 755 logs cache
   ```

3. **内存不足**
   ```bash
   # 检查系统资源
   free -h
   df -h
   ```

4. **容器无法启动**
   ```bash
   # 查看详细错误信息
   docker-compose logs hdsc-query-app
   ```

### 日志位置
- 应用日志：`logs/app.log`
- 错误日志：`logs/error.log`
- Docker日志：`docker-compose logs`

## 安全建议

1. **防火墙配置**
   - 只开放必要的端口（5000）
   - 配置适当的防火墙规则

2. **定期更新**
   - 定期更新Docker镜像
   - 监控安全补丁

3. **备份策略**
   - 定期备份配置文件
   - 备份重要的日志文件

## 性能优化

1. **资源监控**
   ```bash
   # 监控容器资源使用
   docker stats hdsc-query-app
   ```

2. **日志轮转**
   - 配置日志轮转以避免磁盘空间不足
   - 定期清理旧日志文件

3. **缓存优化**
   - 监控缓存目录大小
   - 根据需要调整缓存配置

## 国内镜像源配置

### Docker镜像源
项目已配置使用以下国内镜像源以提高构建速度：
- **Docker镜像**: 阿里云容器镜像服务 `registry.cn-hangzhou.aliyuncs.com`
- **系统包**: 清华大学镜像源 `mirrors.tuna.tsinghua.edu.cn`
- **Python包**: 清华大学PyPI镜像 `pypi.tuna.tsinghua.edu.cn`
- **NPM包**: NPM淘宝镜像 `registry.npmmirror.com`

### 推荐的Docker守护进程配置
为获得最佳构建体验，建议配置多个镜像源：
```json
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerproxy.com"
  ]
}
```

### 构建时镜像源说明
- **构建阶段**: 使用阿里云Node.js镜像，配置NPM淘宝镜像源
- **生产阶段**: 使用阿里云Python镜像，配置清华大学APT和PyPI镜像源
- **网络优化**: 所有网络请求都通过国内镜像源，大大提高构建速度
@echo off
setlocal enabledelayedexpansion

:: 设置变量
set IMAGE_NAME=hdsc-query-app
set IMAGE_TAG=latest
set EXPORT_FILENAME=hdsc-query-app-optimized.tar

echo 正在清理旧的构建...
docker system prune -f

echo 开始构建优化版Docker镜像...
docker build -t %IMAGE_NAME%:%IMAGE_TAG% -f Dockerfile.optimized .

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    exit /b %ERRORLEVEL%
)

echo 构建成功！

echo 正在导出Docker镜像...
docker save -o %EXPORT_FILENAME% %IMAGE_NAME%:%IMAGE_TAG%

if %ERRORLEVEL% neq 0 (
    echo 导出失败！
    exit /b %ERRORLEVEL%
)

echo 导出成功！
echo 镜像文件已保存为: %EXPORT_FILENAME%

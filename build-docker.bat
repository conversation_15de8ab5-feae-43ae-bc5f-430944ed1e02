@echo off
REM HDSC查询应用 Docker构建脚本 (Windows版本)
REM 使用方法：build-docker.bat [版本号]

setlocal enabledelayedexpansion

REM 默认版本号
if "%1"=="" (
    set VERSION=v1.0
) else (
    set VERSION=%1
)

set IMAGE_NAME=hdsc-query-app
set FULL_IMAGE_NAME=%IMAGE_NAME%:%VERSION%

echo ==========================================
echo HDSC查询应用 Docker构建脚本
echo ==========================================
echo 镜像名称: %FULL_IMAGE_NAME%
echo 构建时间: %date% %time%
echo ==========================================

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未运行或无法访问
    exit /b 1
)

REM 检查前端资源是否已构建
if not exist "app\static\dist" (
    echo 警告: 前端资源未构建，正在构建...
    where npm >nul 2>&1
    if errorlevel 1 (
        echo 错误: 未找到npm，请先安装Node.js或手动构建前端资源
        exit /b 1
    )
    npm install
    npm run build
    echo 前端资源构建完成
)

REM 选择Dockerfile
set DOCKERFILE=Dockerfile
if exist "Dockerfile.simple" (
    echo 使用简化版Dockerfile（单阶段构建）
    set DOCKERFILE=Dockerfile.simple
)

REM 构建镜像
echo 开始构建Docker镜像...
docker build -f %DOCKERFILE% -t %FULL_IMAGE_NAME% .

if errorlevel 1 (
    echo ==========================================
    echo Docker镜像构建失败！
    echo 请检查错误信息并重试
    echo ==========================================
    exit /b 1
)

echo ==========================================
echo Docker镜像构建成功！
echo 镜像名称: %FULL_IMAGE_NAME%
echo ==========================================

REM 显示镜像信息
docker images %IMAGE_NAME%

REM 询问是否导出镜像
set /p EXPORT_CHOICE="是否导出镜像为tar文件？(y/N): "
if /i "%EXPORT_CHOICE%"=="y" (
    set TAR_FILE=%IMAGE_NAME%-%VERSION%.tar
    echo 正在导出镜像到 !TAR_FILE!...
    docker save -o !TAR_FILE! %FULL_IMAGE_NAME%
    echo 镜像已导出到: !TAR_FILE!
    dir !TAR_FILE!
)

REM 询问是否运行测试
set /p TEST_CHOICE="是否运行测试容器？(y/N): "
if /i "%TEST_CHOICE%"=="y" (
    echo 启动测试容器...
    docker run -d --name hdsc-test-%VERSION% -p 5001:5000 %FULL_IMAGE_NAME%
    echo 测试容器已启动，访问地址: http://localhost:5001
    echo 停止容器命令: docker stop hdsc-test-%VERSION%
    echo 删除容器命令: docker rm hdsc-test-%VERSION%
)

pause
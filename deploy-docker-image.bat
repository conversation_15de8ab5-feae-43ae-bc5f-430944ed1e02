@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM HDSC查询应用 - Docker镜像快速部署脚本 (Windows版)
REM 版本: v1.0
REM 用途: 在Windows服务器上快速部署Docker镜像

echo 🚀 HDSC查询应用 - Docker快速部署脚本 (Windows版)
echo 版本: v1.0
echo.

REM 配置变量
set IMAGE_FILE=hdsc-query-app-v1.0.tar
set IMAGE_NAME=hdsc-query-app:v1.0
set CONTAINER_NAME=hdsc-query-app
set HOST_PORT=5000
set CONTAINER_PORT=5000

REM 检查Docker是否安装
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker服务未运行，请启动Docker Desktop
    pause
    exit /b 1
)
echo [SUCCESS] Docker环境检查通过

REM 检查镜像文件
echo [INFO] 检查镜像文件...
if not exist "%IMAGE_FILE%" (
    echo [ERROR] 镜像文件 %IMAGE_FILE% 不存在
    echo [INFO] 请确保镜像文件在当前目录下
    pause
    exit /b 1
)

for %%A in ("%IMAGE_FILE%") do set FILE_SIZE=%%~zA
set /a FILE_SIZE_MB=!FILE_SIZE!/1024/1024
echo [SUCCESS] 找到镜像文件: %IMAGE_FILE% (大小: !FILE_SIZE_MB!MB)

REM 停止并删除现有容器
echo [INFO] 清理现有容器...
docker ps -q -f name=%CONTAINER_NAME% >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] 停止现有容器: %CONTAINER_NAME%
    docker stop %CONTAINER_NAME% >nul 2>&1
)

docker ps -aq -f name=%CONTAINER_NAME% >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] 删除现有容器: %CONTAINER_NAME%
    docker rm %CONTAINER_NAME% >nul 2>&1
)
echo [SUCCESS] 容器清理完成

REM 导入Docker镜像
echo [INFO] 导入Docker镜像...
docker images -q %IMAGE_NAME% >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] 镜像 %IMAGE_NAME% 已存在，将删除旧镜像
    docker rmi %IMAGE_NAME% >nul 2>&1
)

echo [INFO] 正在导入镜像文件，请稍候...
docker load -i "%IMAGE_FILE%"
if errorlevel 1 (
    echo [ERROR] 镜像导入失败
    pause
    exit /b 1
)
echo [SUCCESS] 镜像导入完成

REM 创建日志目录
if not exist "logs" mkdir logs

REM 运行容器
echo [INFO] 启动应用容器...
docker run -d ^
    --name %CONTAINER_NAME% ^
    -p %HOST_PORT%:%CONTAINER_PORT% ^
    -v "%CD%\logs:/app/logs" ^
    -e FLASK_ENV=production ^
    --restart unless-stopped ^
    %IMAGE_NAME%

if errorlevel 1 (
    echo [ERROR] 容器启动失败
    pause
    exit /b 1
)
echo [SUCCESS] 容器启动成功

REM 验证部署
echo [INFO] 验证部署状态...
timeout /t 5 /nobreak >nul

REM 检查容器状态
docker ps | findstr %CONTAINER_NAME% >nul
if errorlevel 1 (
    echo [ERROR] 容器启动失败
    echo [INFO] 查看容器日志:
    docker logs %CONTAINER_NAME%
    pause
    exit /b 1
)

REM 检查应用响应
echo [INFO] 等待应用启动...
set /a RETRY_COUNT=0
:check_health
set /a RETRY_COUNT+=1
curl -s "http://localhost:%HOST_PORT%/health" >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] 应用健康检查通过
    goto health_ok
)

if !RETRY_COUNT! geq 15 (
    echo [WARNING] 健康检查超时，但容器正在运行
    echo [INFO] 请手动检查: http://localhost:%HOST_PORT%
    goto health_ok
)

timeout /t 2 /nobreak >nul
goto check_health

:health_ok
echo [SUCCESS] 部署验证完成

REM 显示部署信息
echo.
echo ==========================================
echo 🎉 HDSC查询应用部署完成!
echo ==========================================
echo 📋 部署信息:
echo    - 容器名称: %CONTAINER_NAME%
echo    - 镜像版本: %IMAGE_NAME%
echo    - 访问地址: http://localhost:%HOST_PORT%
echo    - 健康检查: http://localhost:%HOST_PORT%/health
echo.
echo 🔧 管理命令:
echo    - 查看状态: docker ps
echo    - 查看日志: docker logs %CONTAINER_NAME%
echo    - 停止应用: docker stop %CONTAINER_NAME%
echo    - 重启应用: docker restart %CONTAINER_NAME%
echo.
echo 📁 日志文件: .\logs\
echo ==========================================
echo.
echo [SUCCESS] 部署完成! 🎉
echo.
echo 按任意键打开应用...
pause >nul

REM 打开浏览器
start http://localhost:%HOST_PORT%

endlocal
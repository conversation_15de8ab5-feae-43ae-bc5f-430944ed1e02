/**
 * 移动端优化样式
 * 专门针对移动设备的响应式设计
 */

/* ==================== 移动端断点 ==================== */
@media (max-width: 768px) {
    /* 主容器调整 */
    .modern-overdue-container {
        background: var(--light-gray);
    }
    
    .main-content {
        margin-left: 0;
        padding: var(--spacing-md);
    }
    
    /* 页面头部移动端优化 */
    .page-header {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-lg);
    }
    
    .header-left {
        text-align: center;
    }
    
    .page-title {
        font-size: 1.5rem;
        justify-content: center;
    }
    
    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-btn {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .action-btn .btn-text {
        display: none;
    }
    
    /* 状态卡片移动端优化 */
    .status-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .status-card {
        padding: var(--spacing-md);
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .card-value {
        font-size: 1.1rem;
    }
    
    /* 搜索控制区移动端优化 */
    .search-controls {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .search-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .search-box {
        min-width: auto;
    }
    
    .search-input-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .search-input {
        padding-left: var(--spacing-md);
    }
    
    .search-icon {
        display: none;
    }
    
    .search-btn {
        margin-left: 0;
        width: 100%;
        justify-content: center;
    }
    
    .clear-search-btn {
        position: static;
        width: 100%;
        justify-content: center;
        margin-top: var(--spacing-sm);
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .page-size-selector {
        flex-direction: column;
        text-align: center;
    }
    
    /* 桌面端表格隐藏 */
    .desktop-table-view {
        display: none;
    }
    
    /* 移动端卡片视图显示 */
    .mobile-card-view {
        display: block;
        padding: var(--spacing-md);
    }
    
    .cards-container {
        gap: var(--spacing-md);
    }
    
    .data-card {
        border-radius: var(--border-radius-md);
    }
    
    .card-header {
        padding: var(--spacing-md);
    }
    
    .card-title {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .order-number {
        font-size: 0.9rem;
    }
    
    .card-summary {
        padding: var(--spacing-md);
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }
    
    .card-details {
        padding: var(--spacing-md);
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .detail-value {
        text-align: left;
        max-width: 100%;
    }
    
    /* 分页移动端优化 */
    .pagination-wrapper {
        padding: var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .pagination-btn .btn-text {
        display: none;
    }
    
    .page-numbers {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .page-btn {
        width: 35px;
        height: 35px;
        font-size: 0.85rem;
    }
    
    /* 错误和空状态移动端优化 */
    .error-state,
    .empty-state {
        padding: var(--spacing-xl);
    }
    
    .error-icon,
    .empty-icon {
        font-size: 3rem;
        margin-bottom: var(--spacing-md);
    }
    
    .error-title,
    .empty-title {
        font-size: 1.25rem;
        margin-bottom: var(--spacing-sm);
    }
    
    .error-message,
    .empty-message {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-md);
    }
}

/* ==================== 小屏幕设备优化 ==================== */
@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .page-header {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .page-title {
        font-size: 1.25rem;
    }
    
    .status-cards {
        margin-bottom: var(--spacing-md);
    }
    
    .status-card {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .search-controls {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-card-view {
        padding: var(--spacing-sm);
    }
    
    .card-summary {
        grid-template-columns: 1fr;
    }
    
    .pagination-wrapper {
        padding: var(--spacing-md);
    }
    
    .page-numbers {
        gap: 2px;
    }
    
    .page-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .pagination-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.85rem;
    }
}

/* ==================== 超小屏幕设备优化 ==================== */
@media (max-width: 320px) {
    .main-content {
        padding: var(--spacing-xs);
    }
    
    .page-header,
    .search-controls {
        padding: var(--spacing-sm);
    }
    
    .status-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .card-icon {
        align-self: center;
    }
    
    .header-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
    }
    
    .export-menu {
        right: auto;
        left: 0;
        width: 100%;
    }
    
    .page-numbers {
        display: none;
    }
    
    .pagination-controls {
        justify-content: space-between;
        width: 100%;
    }
}

/* ==================== 触摸设备优化 ==================== */
@media (hover: none) and (pointer: coarse) {
    /* 增加触摸目标大小 */
    .action-btn,
    .pagination-btn,
    .page-btn,
    .card-expand-btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .search-btn {
        min-height: 48px;
    }
    
    /* 移除悬停效果 */
    .action-btn:hover,
    .pagination-btn:hover,
    .page-btn:hover,
    .data-card:hover {
        transform: none;
        box-shadow: initial;
    }
    
    /* 添加触摸反馈 */
    .action-btn:active,
    .pagination-btn:active,
    .page-btn:active {
        transform: scale(0.95);
    }
    
    .data-card:active {
        transform: scale(0.98);
    }
}

/* ==================== 横屏模式优化 ==================== */
@media (max-width: 768px) and (orientation: landscape) {
    .status-cards {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .search-section {
        flex-direction: row;
        align-items: center;
    }
    
    .search-box {
        flex: 1;
    }
    
    .filter-controls {
        flex-shrink: 0;
    }
    
    .card-summary {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ==================== 高对比度模式支持 ==================== */
@media (prefers-contrast: high) {
    .status-card,
    .data-card,
    .search-controls {
        border: 2px solid var(--text-color);
    }
    
    .status-badge {
        border: 1px solid var(--white);
    }
    
    .pagination-btn,
    .page-btn {
        border: 2px solid var(--text-color);
    }
}

/* ==================== 减少动画模式支持 ==================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}

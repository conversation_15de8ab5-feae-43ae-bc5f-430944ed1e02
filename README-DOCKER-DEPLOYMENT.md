# HDSC查询应用 - Docker镜像部署包

## 📦 包含文件

- `hdsc-query-app-v1.0.tar` - Docker镜像文件 (869MB)
- `deploy-docker-image.sh` - Linux/Mac部署脚本
- `deploy-docker-image.bat` - Windows部署脚本
- `DOCKER_IMAGE_EXPORT_REPORT.md` - 详细部署文档
- `README-DOCKER-DEPLOYMENT.md` - 本文件

## 🚀 快速部署

### 方法一：使用自动化脚本（推荐）

#### Linux/Mac系统
```bash
# 给脚本执行权限
chmod +x deploy-docker-image.sh

# 运行部署脚本
./deploy-docker-image.sh
```

#### Windows系统
```cmd
# 双击运行或在命令行执行
deploy-docker-image.bat
```

### 方法二：手动部署

#### 1. 导入镜像
```bash
docker load -i hdsc-query-app-v1.0.tar
```

#### 2. 运行容器
```bash
docker run -d -p 5000:5000 --name hdsc-query-app hdsc-query-app:v1.0
```

#### 3. 访问应用
打开浏览器访问: http://localhost:5000

## 🔧 配置选项

### 环境变量
- `FLASK_ENV=production` - 生产环境模式
- `API_BASE_URL=http://your-api:5000` - 后端API地址

### 端口映射
- 默认端口: 5000
- 自定义端口: `-p 8080:5000`

### 数据卷
- 日志持久化: `-v /host/logs:/app/logs`
- 下载文件: `-v /host/downloads:/app/download`

## 📋 管理命令

```bash
# 查看容器状态
docker ps

# 查看应用日志
docker logs hdsc-query-app

# 停止应用
docker stop hdsc-query-app

# 重启应用
docker restart hdsc-query-app

# 删除容器
docker rm hdsc-query-app

# 删除镜像
docker rmi hdsc-query-app:v1.0
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 使用其他端口
   docker run -d -p 8080:5000 --name hdsc-query-app hdsc-query-app:v1.0
   ```

2. **容器启动失败**
   ```bash
   # 查看详细日志
   docker logs hdsc-query-app
   ```

3. **无法访问应用**
   - 检查防火墙设置
   - 确认端口映射正确
   - 验证Docker服务状态

### 健康检查
```bash
# 检查应用健康状态
curl http://localhost:5000/health
```

## 📊 系统要求

- **操作系统**: Linux, macOS, Windows
- **Docker版本**: 20.10+
- **内存**: 至少1GB可用
- **磁盘空间**: 至少2GB可用
- **网络**: 需要访问外部API

## 📞 技术支持

如遇问题，请查看：
1. `DOCKER_IMAGE_EXPORT_REPORT.md` - 详细技术文档
2. 容器日志: `docker logs hdsc-query-app`
3. 应用日志: `./logs/` 目录

---

**版本**: v1.0  
**更新时间**: 2024年最新  
**镜像大小**: 849MB  
**部署时间**: 约1-2分钟
#!/bin/bash

# HDSC查询应用 Docker构建脚本
# 使用方法：./build-docker.sh [版本号]

set -e

# 默认版本号
VERSION=${1:-"v1.0"}
IMAGE_NAME="hdsc-query-app"
FULL_IMAGE_NAME="${IMAGE_NAME}:${VERSION}"

echo "=========================================="
echo "HDSC查询应用 Docker构建脚本"
echo "=========================================="
echo "镜像名称: ${FULL_IMAGE_NAME}"
echo "构建时间: $(date)"
echo "=========================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行或无法访问"
    exit 1
fi

# 检查前端资源是否已构建
if [ ! -d "app/static/dist" ]; then
    echo "警告: 前端资源未构建，正在构建..."
    if command -v npm > /dev/null 2>&1; then
        npm install
        npm run build
        echo "前端资源构建完成"
    else
        echo "错误: 未找到npm，请先安装Node.js或手动构建前端资源"
        exit 1
    fi
fi

# 选择Dockerfile
if [ -f "Dockerfile.optimized" ] && ping -c 1 registry-1.docker.io > /dev/null 2>&1; then
    echo "使用优化版Dockerfile（多阶段构建）"
    DOCKERFILE="Dockerfile.optimized"
elif [ -f "Dockerfile.simple" ]; then
    echo "使用简化版Dockerfile（单阶段构建）"
    DOCKERFILE="Dockerfile.simple"
else
    echo "使用默认Dockerfile"
    DOCKERFILE="Dockerfile"
fi

# 构建镜像
echo "开始构建Docker镜像..."
docker build -f ${DOCKERFILE} -t ${FULL_IMAGE_NAME} .

if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "Docker镜像构建成功！"
    echo "镜像名称: ${FULL_IMAGE_NAME}"
    echo "=========================================="
    
    # 显示镜像信息
    docker images ${IMAGE_NAME}
    
    # 询问是否导出镜像
    read -p "是否导出镜像为tar文件？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        TAR_FILE="${IMAGE_NAME}-${VERSION}.tar"
        echo "正在导出镜像到 ${TAR_FILE}..."
        docker save -o ${TAR_FILE} ${FULL_IMAGE_NAME}
        echo "镜像已导出到: ${TAR_FILE}"
        ls -lh ${TAR_FILE}
    fi
    
    # 询问是否运行测试
    read -p "是否运行测试容器？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "启动测试容器..."
        docker run -d --name hdsc-test-${VERSION} -p 5001:5000 ${FULL_IMAGE_NAME}
        echo "测试容器已启动，访问地址: http://localhost:5001"
        echo "停止容器命令: docker stop hdsc-test-${VERSION}"
        echo "删除容器命令: docker rm hdsc-test-${VERSION}"
    fi
    
else
    echo "=========================================="
    echo "Docker镜像构建失败！"
    echo "请检查错误信息并重试"
    echo "=========================================="
    exit 1
fi
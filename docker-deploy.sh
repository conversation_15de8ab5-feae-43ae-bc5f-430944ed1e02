#!/bin/bash

# HDSC查询系统Docker部署脚本
# 适用于Ubuntu AMD64平台
# 作者：AI Assistant
# 更新日期：2024年12月

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $1]${NC} $2"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    log_info "Docker版本: $(docker --version)"
    log_info "Docker Compose版本: $(docker-compose --version)"
}

# 检查项目文件
check_project_files() {
    local required_files=("Dockerfile" "docker-compose.yml" "requirements.txt" "run.py" "app")
    
    for file in "${required_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "缺少必要文件或目录: $file"
            exit 1
        fi
    done
    
    log_info "项目文件检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    mkdir -p logs cache
    chmod 755 logs cache
    log_info "目录创建完成"
}

# 停止并删除现有容器
cleanup_existing() {
    log_info "清理现有容器..."
    
    if docker ps -a | grep -q "hdsc-query-app"; then
        log_info "停止现有容器..."
        docker-compose down || true
        
        # 删除现有镜像（可选）
        if [ "$1" = "--rebuild" ]; then
            log_info "删除现有镜像..."
            docker rmi hdsc-query-app:latest || true
        fi
    fi
    
    log_info "清理完成"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    # 使用docker-compose构建
    docker-compose build --no-cache
    
    if [ $? -eq 0 ]; then
        log_info "镜像构建成功"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待服务就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
            log_info "服务已就绪"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查容器状态
    local container_status=$(docker inspect --format='{{.State.Status}}' hdsc-query-app 2>/dev/null || echo "not_found")
    log_info "容器状态: $container_status"
    
    # 检查端口监听
    if docker exec hdsc-query-app netstat -tlnp 2>/dev/null | grep -q ":5000" || \
       docker exec hdsc-query-app ss -tlnp 2>/dev/null | grep -q ":5000"; then
        log_info "✓ 端口5000正在监听"
    else
        log_warn "✗ 端口5000未在监听"
    fi
    
    # 检查HTTP响应
    if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
        log_info "✓ HTTP连接测试成功"
    else
        log_warn "✗ HTTP连接测试失败"
    fi
    
    # 显示容器日志（最后20行）
    log_info "容器日志（最后20行）:"
    docker-compose logs --tail=20 hdsc-query-app
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    echo ""
    echo "=== 部署信息 ==="
    echo "服务地址: http://localhost:5000"
    echo "容器名称: hdsc-query-app"
    echo "网络名称: hdsc-network"
    echo ""
    echo "=== 管理命令 ==="
    echo "查看服务状态: docker-compose ps"
    echo "查看日志: docker-compose logs -f hdsc-query-app"
    echo "停止服务: docker-compose down"
    echo "重启服务: docker-compose restart"
    echo "进入容器: docker exec -it hdsc-query-app bash"
    echo ""
    echo "=== 目录挂载 ==="
    echo "日志目录: ./logs -> /app/logs"
    echo "缓存目录: ./cache -> /app/cache"
    echo "文档目录: ./AHT, ./BHT, ./HZ -> /app/AHT, /app/BHT, /app/HZ"
}

# 主函数
main() {
    log_step 1 "检查环境"
    check_docker
    check_project_files
    
    log_step 2 "准备部署"
    create_directories
    cleanup_existing "$1"
    
    log_step 3 "构建镜像"
    build_image
    
    log_step 4 "启动服务"
    start_services
    
    log_step 5 "等待服务就绪"
    if wait_for_service; then
        log_step 6 "健康检查"
        health_check
        
        log_step 7 "部署完成"
        show_deployment_info
    else
        log_error "部署失败，请检查日志"
        docker-compose logs hdsc-query-app
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "HDSC查询系统Docker部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --rebuild    重新构建镜像（删除现有镜像）"
    echo "  --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0           # 正常部署"
    echo "  $0 --rebuild # 重新构建并部署"
}

# 解析命令行参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --rebuild)
        main --rebuild
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac

#!/bin/bash

# HDSC查询系统部署状态检查脚本
# 用于检查Docker部署的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "=== HDSC查询系统部署状态检查 ==="
echo ""

# 检查Docker服务
echo "1. 检查Docker服务状态..."
if sudo docker version > /dev/null 2>&1; then
    log_success "Docker服务正在运行"
else
    log_error "Docker服务未运行"
    exit 1
fi

# 检查容器状态
echo ""
echo "2. 检查容器状态..."
if sudo docker ps | grep -q "hdsc-query-app"; then
    container_status=$(sudo docker inspect --format='{{.State.Status}}' hdsc-query-app 2>/dev/null)
    container_health=$(sudo docker inspect --format='{{.State.Health.Status}}' hdsc-query-app 2>/dev/null || echo "none")
    
    log_success "容器正在运行"
    log_info "容器状态: $container_status"
    log_info "健康状态: $container_health"
else
    log_error "hdsc-query-app容器未运行"
    exit 1
fi

# 检查端口监听
echo ""
echo "3. 检查端口监听..."
if netstat -tlnp 2>/dev/null | grep -q ":5000" || ss -tlnp 2>/dev/null | grep -q ":5000"; then
    log_success "端口5000正在监听"
else
    log_warn "端口5000未在监听（可能正在启动中）"
fi

# 检查HTTP响应
echo ""
echo "4. 检查HTTP服务..."
if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
    log_success "HTTP服务响应正常"
    
    # 获取响应状态码
    status_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000)
    log_info "HTTP状态码: $status_code"
else
    log_error "HTTP服务无响应"
    exit 1
fi

# 检查日志
echo ""
echo "5. 检查最近日志..."
if sudo docker logs --tail=5 hdsc-query-app 2>/dev/null | grep -q "定时任务初始化成功"; then
    log_success "应用初始化成功"
else
    log_warn "应用可能仍在初始化中"
fi

# 检查挂载目录
echo ""
echo "6. 检查挂载目录..."
for dir in logs cache AHT BHT HZ; do
    if [ -d "./$dir" ]; then
        log_success "目录 $dir 存在"
    else
        log_warn "目录 $dir 不存在"
    fi
done

# 显示资源使用情况
echo ""
echo "7. 容器资源使用情况..."
sudo docker stats --no-stream hdsc-query-app 2>/dev/null | tail -n +2 | while read line; do
    log_info "资源使用: $line"
done

echo ""
echo "=== 检查完成 ==="
echo ""
echo "如果所有检查都通过，您可以通过以下方式访问应用："
echo "- 浏览器访问: http://localhost:5000"
echo "- 查看日志: docker-compose logs -f hdsc-query-app"
echo "- 进入容器: docker exec -it hdsc-query-app bash"
echo ""
echo "管理命令："
echo "- 停止服务: docker-compose down"
echo "- 重启服务: docker-compose restart"
echo "- 查看状态: docker-compose ps"

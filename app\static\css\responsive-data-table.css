/**
 * 响应式数据表格样式
 * 支持桌面端表格和移动端卡片视图
 */

/* ==================== 数据容器 ==================== */
.data-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

/* ==================== 结果统计 ==================== */
.result-summary {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-bottom: 1px solid var(--medium-gray);
}

.summary-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-color);
}

.summary-info i {
    color: var(--primary-color);
}

/* ==================== 桌面端表格视图 ==================== */
.desktop-table-view {
    display: block;
}

.table-container {
    overflow-x: auto;
    overflow-y: hidden;
}

.modern-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.table-header {
    background: var(--light-gray);
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--medium-gray);
    cursor: pointer;
    transition: background var(--transition-fast);
    position: relative;
    white-space: nowrap;
}

.table-header:hover {
    background: var(--medium-gray);
}

.header-text {
    margin-right: var(--spacing-sm);
}

.sort-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.sort-icon.active {
    color: var(--primary-color);
}

.table-row {
    transition: background var(--transition-fast);
}

.table-row:hover {
    background: rgba(0, 123, 255, 0.05);
}

.table-row:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.table-row:nth-child(even):hover {
    background: rgba(0, 123, 255, 0.05);
}

.table-cell {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--medium-gray);
    vertical-align: middle;
}

/* ==================== 表格内容样式 ==================== */
.amount-value {
    font-weight: 600;
    color: var(--success-color);
}

.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.status-badge.danger {
    background: var(--danger-color);
    color: var(--white);
}

.status-badge.primary {
    background: var(--primary-color);
    color: var(--white);
}

.status-badge.info {
    background: var(--info-color);
    color: var(--white);
}

.date-value {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.text-value {
    color: var(--text-color);
}

.empty-value {
    color: var(--text-muted);
    font-style: italic;
}

/* ==================== 响应式列管理 ==================== */
.table-container {
    position: relative;
    overflow: hidden;
}

.table-wrapper {
    position: relative;
    overflow-x: auto;
    overflow-y: visible;
}

/* 列控制按钮 */
.column-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    gap: var(--spacing-xs);
}

.column-toggle-btn {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    font-size: 0.75rem;
    color: var(--text-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.column-toggle-btn:hover {
    background: var(--light-gray);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.column-toggle-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* 列显示控制 */
.table-responsive .table th,
.table-responsive .table td {
    white-space: nowrap;
    min-width: 120px;
}

/* 隐藏的列 */
.table th.column-hidden,
.table td.column-hidden {
    display: none;
}

/* 列展开状态 */
.table.columns-expanded {
    min-width: 100%;
}

.table.columns-expanded th,
.table.columns-expanded td {
    min-width: 150px;
}

/* 列折叠指示器 */
.columns-indicator {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: linear-gradient(to right, transparent, var(--white));
    padding: var(--spacing-sm);
    font-size: 0.75rem;
    color: var(--text-muted);
    pointer-events: none;
}

/* 滚动阴影效果 */
.table-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.table-wrapper.has-scroll::after {
    opacity: 1;
}

/* ==================== 移动端卡片视图 ==================== */
.mobile-card-view {
    display: none;
    padding: var(--spacing-lg);
}

.cards-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.data-card {
    background: var(--white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.data-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-bottom: 1px solid var(--medium-gray);
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.order-number {
    font-weight: 600;
    color: var(--text-color);
    font-size: 1rem;
}

.overdue-badge {
    background: var(--danger-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.card-actions {
    display: flex;
    align-items: center;
}

.card-expand-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.card-expand-btn:hover {
    background: var(--medium-gray);
    color: var(--text-color);
}

.card-expand-btn i {
    transition: transform var(--transition-normal);
}

.card-summary {
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-value {
    font-weight: 500;
    color: var(--text-color);
}

.item-value.amount {
    color: var(--success-color);
    font-weight: 600;
}

.card-details {
    display: none;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--medium-gray);
    background: rgba(248, 249, 250, 0.5);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(233, 236, 239, 0.5);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.detail-value {
    font-size: 0.875rem;
    color: var(--text-color);
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

/* ==================== 分页样式 ==================== */
.pagination-wrapper {
    padding: var(--spacing-xl);
    background: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.pagination-nav {
    display: flex;
    align-items: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--white);
    color: var(--text-color);
    text-decoration: none;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.page-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--white);
    color: var(--text-color);
    text-decoration: none;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.page-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-ellipsis {
    padding: var(--spacing-md);
    color: var(--text-muted);
}

/* ==================== 错误和空状态 ==================== */
.error-state,
.empty-state {
    padding: var(--spacing-xxl);
    text-align: center;
    color: var(--text-muted);
}

.error-icon,
.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.error-icon {
    color: var(--danger-color);
}

.empty-icon {
    color: var(--text-muted);
}

.error-title,
.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.error-message,
.empty-message {
    font-size: 1rem;
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.retry-btn,
.load-data-btn,
.clear-search-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.retry-btn:hover,
.load-data-btn:hover,
.clear-search-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

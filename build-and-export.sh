#!/bin/bash

echo "开始构建 HDSC Query App Docker 镜像..."
echo "使用国内镜像源：阿里云Docker镜像仓库 + 清华大学源"
echo

# 构建Docker镜像
echo "[1/3] 构建Docker镜像..."
docker build -t hdsc-query-app:production .
if [ $? -ne 0 ]; then
    echo "错误：Docker镜像构建失败！"
    exit 1
fi

echo
echo "[2/3] 添加latest标签..."
docker tag hdsc-query-app:production hdsc-query-app:latest

echo
echo "[3/3] 导出Docker镜像为tar文件..."
docker save -o hdsc-query-app-production.tar hdsc-query-app:production hdsc-query-app:latest
if [ $? -ne 0 ]; then
    echo "错误：Docker镜像导出失败！"
    exit 1
fi

echo
echo "========================================"
echo "构建和导出完成！"
echo "========================================"
echo "镜像文件: hdsc-query-app-production.tar"
echo "镜像大小: $(du -h hdsc-query-app-production.tar | cut -f1)"
echo
echo "在生产服务器上使用以下命令加载镜像:"
echo "docker load -i hdsc-query-app-production.tar"
echo "docker-compose up -d"
echo